apiVersion: apps/v1
kind: Deployment
metadata:
  name: affiliate-platform
  labels:
    app: affiliate-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: affiliate-platform
  template:
    metadata:
      labels:
        app: affiliate-platform
    spec:
      containers:
      - name: app
        image: ghcr.io/your-username/affiliate-platform:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: affiliate-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: affiliate-secrets
              key: jwt-secret
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: affiliate-secrets
              key: stripe-secret-key
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: affiliate-secrets
              key: stripe-webhook-secret
        - name: APP_URL
          valueFrom:
            configMapKeyRef:
              name: affiliate-config
              key: app-url
        - name: AFFILIATE_LINK_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: affiliate-config
              key: affiliate-link-domain
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]

---
apiVersion: v1
kind: Service
metadata:
  name: affiliate-platform-service
  labels:
    app: affiliate-platform
spec:
  selector:
    app: affiliate-platform
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: affiliate-platform-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: affiliate-platform-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: affiliate-platform-service
            port:
              number: 80

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: affiliate-config
data:
  app-url: "https://your-domain.com"
  affiliate-link-domain: "https://your-domain.com/go"

---
apiVersion: v1
kind: Secret
metadata:
  name: affiliate-secrets
type: Opaque
data:
  # Base64 encoded values - replace with your actual secrets
  database-url: ********************************************************************
  jwt-secret: eW91ci1zdXBlci1zZWNyZXQtand0LWtleQ==
  stripe-secret-key: c2tfdGVzdF95b3VyX3N0cmlwZV9zZWNyZXRfa2V5
  stripe-webhook-secret: d2hzZWNfeW91cl93ZWJob29rX3NlY3JldA==

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: affiliate-platform-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: affiliate-platform
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: affiliate-platform-pdb
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: affiliate-platform
