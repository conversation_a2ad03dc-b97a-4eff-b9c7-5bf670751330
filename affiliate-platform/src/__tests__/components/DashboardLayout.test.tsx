import { render, screen, fireEvent } from '@testing-library/react'
import DashboardLayout from '@/components/DashboardLayout'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}))

describe('DashboardLayout', () => {
  const mockUser = {
    id: 'user123',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'AFFILIATE' as const,
    affiliate: {
      id: 'affiliate123',
      displayName: 'Test Affiliate',
      totalEarnings: 150.75,
      pendingEarnings: 25.50,
    },
  }

  beforeEach(() => {
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => JSON.stringify(mockUser)),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should render dashboard layout with affiliate navigation', () => {
    render(
      <DashboardLayout title="Test Dashboard" user={mockUser}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    expect(screen.getByText('AffiliateHub')).toBeInTheDocument()
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
    
    // Check affiliate navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Offers')).toBeInTheDocument()
    expect(screen.getByText('My Links')).toBeInTheDocument()
    expect(screen.getByText('Earnings')).toBeInTheDocument()
    expect(screen.getByText('Analytics')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
  })

  it('should render brand navigation for brand users', () => {
    const brandUser = {
      ...mockUser,
      role: 'BRAND' as const,
      brand: {
        id: 'brand123',
        companyName: 'Test Company',
        status: 'APPROVED',
      },
    }

    render(
      <DashboardLayout title="Brand Dashboard" user={brandUser}>
        <div>Brand Content</div>
      </DashboardLayout>
    )

    // Check brand navigation items
    expect(screen.getByText('Affiliates')).toBeInTheDocument()
    expect(screen.getByText('Payments')).toBeInTheDocument()
    
    // Should show company name
    expect(screen.getByText('Test Company')).toBeInTheDocument()
  })

  it('should display user earnings for affiliates', () => {
    render(
      <DashboardLayout title="Test Dashboard" user={mockUser}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    expect(screen.getByText('Earnings: $150.75')).toBeInTheDocument()
  })

  it('should handle logout', () => {
    const mockPush = jest.fn()
    require('next/navigation').useRouter.mockReturnValue({
      push: mockPush,
    })

    render(
      <DashboardLayout title="Test Dashboard" user={mockUser}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    const logoutButton = screen.getByText('Logout')
    fireEvent.click(logoutButton)

    expect(localStorage.removeItem).toHaveBeenCalledWith('token')
    expect(localStorage.removeItem).toHaveBeenCalledWith('user')
    expect(mockPush).toHaveBeenCalledWith('/')
  })

  it('should show loading state when no user is provided', () => {
    render(
      <DashboardLayout title="Test Dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Should show loading spinner
    expect(document.querySelector('.animate-spin')).toBeInTheDocument()
  })

  it('should redirect to login if no user data is available', () => {
    const mockPush = jest.fn()
    require('next/navigation').useRouter.mockReturnValue({
      push: mockPush,
    })

    // Mock localStorage to return null
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    })

    render(
      <DashboardLayout title="Test Dashboard">
        <div>Test Content</div>
      </DashboardLayout>
    )

    expect(mockPush).toHaveBeenCalledWith('/auth/login')
  })

  it('should toggle mobile sidebar', () => {
    render(
      <DashboardLayout title="Test Dashboard" user={mockUser}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Find the mobile menu button (hamburger menu)
    const mobileMenuButton = document.querySelector('button[aria-label="Open sidebar"]') ||
                            screen.getByText('☰')
    
    expect(mobileMenuButton).toBeInTheDocument()
    
    // Click to open sidebar
    fireEvent.click(mobileMenuButton)
    
    // Should show mobile sidebar overlay
    expect(document.querySelector('.fixed.inset-0')).toBeInTheDocument()
  })

  it('should display user information correctly', () => {
    render(
      <DashboardLayout title="Test Dashboard" user={mockUser}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('Affiliate')).toBeInTheDocument()
  })

  it('should handle missing affiliate data gracefully', () => {
    const userWithoutAffiliate = {
      ...mockUser,
      affiliate: undefined,
    }

    render(
      <DashboardLayout title="Test Dashboard" user={userWithoutAffiliate}>
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Should not crash and should still render the layout
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Test User')).toBeInTheDocument()
  })
})
