import { trackClick, generateAffiliateLink, recordConversion } from '@/lib/affiliate-links'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    affiliateLink: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    clickEvent: {
      create: jest.fn(),
    },
    conversion: {
      create: jest.fn(),
    },
    commission: {
      create: jest.fn(),
    },
    affiliate: {
      update: jest.fn(),
    },
  },
}))

jest.mock('@/lib/fraud-detection', () => ({
  fraudDetection: {
    analyzeClick: jest.fn(),
    recordFraudAttempt: jest.fn(),
  },
}))

describe('Affiliate Link Tracking Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('generateAffiliateLink', () => {
    it('should create a new affiliate link', async () => {
      const { prisma } = require('@/lib/prisma')
      
      const mockAffiliateLink = {
        id: 'link123',
        affiliateId: 'affiliate123',
        offerId: 'offer123',
        linkCode: 'abc123def456',
        originalUrl: 'https://example.com/product',
        isActive: true,
        clicks: 0,
        conversionCount: 0,
        offer: {
          id: 'offer123',
          title: 'Test Offer',
          brand: {
            id: 'brand123',
            companyName: 'Test Brand',
          },
        },
        affiliate: {
          id: 'affiliate123',
          user: {
            id: 'user123',
            name: 'Test Affiliate',
          },
        },
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(null) // No existing link
      prisma.affiliateLink.create.mockResolvedValue(mockAffiliateLink)

      const result = await generateAffiliateLink({
        affiliateId: 'affiliate123',
        offerId: 'offer123',
        originalUrl: 'https://example.com/product',
      })

      expect(result).toEqual(mockAffiliateLink)
      expect(prisma.affiliateLink.create).toHaveBeenCalledWith({
        data: {
          affiliateId: 'affiliate123',
          offerId: 'offer123',
          linkCode: expect.any(String),
          originalUrl: 'https://example.com/product',
        },
        include: {
          offer: {
            include: {
              brand: true,
            },
          },
          affiliate: {
            include: {
              user: true,
            },
          },
        },
      })
    })

    it('should return existing affiliate link if already exists', async () => {
      const { prisma } = require('@/lib/prisma')
      
      const existingLink = {
        id: 'existing123',
        affiliateId: 'affiliate123',
        offerId: 'offer123',
        linkCode: 'existing456',
        originalUrl: 'https://example.com/product',
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(existingLink)

      const result = await generateAffiliateLink({
        affiliateId: 'affiliate123',
        offerId: 'offer123',
        originalUrl: 'https://example.com/product',
      })

      expect(result).toEqual(existingLink)
      expect(prisma.affiliateLink.create).not.toHaveBeenCalled()
    })
  })

  describe('trackClick', () => {
    const mockAffiliateLink = {
      id: 'link123',
      affiliateId: 'affiliate123',
      offerId: 'offer123',
      linkCode: 'abc123def456',
      originalUrl: 'https://example.com/product',
      isActive: true,
      clicks: 5,
      conversionCount: 1,
    }

    it('should track a valid click', async () => {
      const { prisma } = require('@/lib/prisma')
      const { fraudDetection } = require('@/lib/fraud-detection')
      
      const mockClickEvent = {
        id: 'click123',
        affiliateLinkId: 'link123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
        timestamp: new Date(),
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(mockAffiliateLink)
      fraudDetection.analyzeClick.mockResolvedValue({
        isValid: true,
        riskScore: 15,
        reasons: [],
        shouldBlock: false,
      })
      prisma.clickEvent.create.mockResolvedValue(mockClickEvent)
      prisma.affiliateLink.update.mockResolvedValue(mockAffiliateLink)

      const result = await trackClick({
        linkCode: 'abc123def456',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
        referrer: 'https://google.com',
        country: 'US',
      })

      expect(result).toEqual({
        clickEvent: mockClickEvent,
        redirectUrl: 'https://example.com/product',
        fraudAnalysis: expect.any(Object),
        blocked: false,
      })

      expect(prisma.clickEvent.create).toHaveBeenCalledWith({
        data: {
          affiliateLinkId: 'link123',
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0...',
          referrer: 'https://google.com',
          country: 'US',
          city: undefined,
          device: undefined,
          browser: undefined,
        },
      })

      expect(prisma.affiliateLink.update).toHaveBeenCalledWith({
        where: { id: 'link123' },
        data: {
          clicks: {
            increment: 1,
          },
        },
      })
    })

    it('should block fraudulent clicks', async () => {
      const { prisma } = require('@/lib/prisma')
      const { fraudDetection } = require('@/lib/fraud-detection')
      
      prisma.affiliateLink.findUnique.mockResolvedValue(mockAffiliateLink)
      fraudDetection.analyzeClick.mockResolvedValue({
        isValid: false,
        riskScore: 85,
        reasons: ['Suspicious user agent', 'High click frequency'],
        shouldBlock: true,
      })

      const result = await trackClick({
        linkCode: 'abc123def456',
        ipAddress: '***********',
        userAgent: 'bot-crawler',
      })

      expect(result).toEqual({
        blocked: true,
        reason: 'Suspicious activity detected',
        riskScore: 85,
      })

      expect(fraudDetection.recordFraudAttempt).toHaveBeenCalled()
      expect(prisma.clickEvent.create).not.toHaveBeenCalled()
      expect(prisma.affiliateLink.update).not.toHaveBeenCalled()
    })

    it('should return null for inactive links', async () => {
      const { prisma } = require('@/lib/prisma')
      
      const inactiveLink = {
        ...mockAffiliateLink,
        isActive: false,
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(inactiveLink)

      const result = await trackClick({
        linkCode: 'abc123def456',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
      })

      expect(result).toBeNull()
    })

    it('should return null for non-existent links', async () => {
      const { prisma } = require('@/lib/prisma')
      
      prisma.affiliateLink.findUnique.mockResolvedValue(null)

      const result = await trackClick({
        linkCode: 'nonexistent',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0...',
      })

      expect(result).toBeNull()
    })
  })

  describe('recordConversion', () => {
    const mockAffiliateLink = {
      id: 'link123',
      affiliateId: 'affiliate123',
      offerId: 'offer123',
      linkCode: 'abc123def456',
      offer: {
        id: 'offer123',
        commissionType: 'PERCENTAGE',
        commissionRate: 5.0,
        fixedCommission: null,
      },
    }

    it('should record a conversion with percentage commission', async () => {
      const { prisma } = require('@/lib/prisma')
      
      const mockConversion = {
        id: 'conversion123',
        affiliateLinkId: 'link123',
        orderId: 'order123',
        orderValue: 100.00,
        commissionValue: 5.00,
        currency: 'USD',
      }

      const mockCommission = {
        id: 'commission123',
        affiliateId: 'affiliate123',
        conversionId: 'conversion123',
        amount: 5.00,
        currency: 'USD',
        status: 'PENDING',
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(mockAffiliateLink)
      prisma.conversion.create.mockResolvedValue(mockConversion)
      prisma.commission.create.mockResolvedValue(mockCommission)
      prisma.affiliateLink.update.mockResolvedValue(mockAffiliateLink)
      prisma.affiliate.update.mockResolvedValue({})

      const result = await recordConversion({
        linkCode: 'abc123def456',
        orderId: 'order123',
        orderValue: 100.00,
        currency: 'USD',
      })

      expect(result).toEqual({
        conversion: mockConversion,
        commission: mockCommission,
      })

      expect(prisma.conversion.create).toHaveBeenCalledWith({
        data: {
          affiliateLinkId: 'link123',
          orderId: 'order123',
          orderValue: 100.00,
          commissionValue: 5.00,
          currency: 'USD',
          conversionData: undefined,
        },
      })

      expect(prisma.commission.create).toHaveBeenCalledWith({
        data: {
          affiliateId: 'affiliate123',
          conversionId: 'conversion123',
          amount: 5.00,
          currency: 'USD',
        },
      })
    })

    it('should record a conversion with fixed commission', async () => {
      const { prisma } = require('@/lib/prisma')
      
      const fixedCommissionLink = {
        ...mockAffiliateLink,
        offer: {
          ...mockAffiliateLink.offer,
          commissionType: 'FIXED_AMOUNT',
          commissionRate: 0,
          fixedCommission: 10.00,
        },
      }

      const mockConversion = {
        id: 'conversion123',
        affiliateLinkId: 'link123',
        orderId: 'order123',
        orderValue: 100.00,
        commissionValue: 10.00,
        currency: 'USD',
      }

      prisma.affiliateLink.findUnique.mockResolvedValue(fixedCommissionLink)
      prisma.conversion.create.mockResolvedValue(mockConversion)
      prisma.commission.create.mockResolvedValue({})
      prisma.affiliateLink.update.mockResolvedValue({})
      prisma.affiliate.update.mockResolvedValue({})

      const result = await recordConversion({
        linkCode: 'abc123def456',
        orderId: 'order123',
        orderValue: 100.00,
      })

      expect(result.conversion.commissionValue).toBe(10.00)
    })

    it('should throw error for non-existent affiliate link', async () => {
      const { prisma } = require('@/lib/prisma')
      
      prisma.affiliateLink.findUnique.mockResolvedValue(null)

      await expect(recordConversion({
        linkCode: 'nonexistent',
        orderId: 'order123',
        orderValue: 100.00,
      })).rejects.toThrow('Affiliate link not found')
    })
  })
})
