import { NextRequest, NextResponse } from 'next/server'
import { trackClick } from '@/lib/affiliate-links'

export async function GET(
  request: NextRequest,
  { params }: { params: { linkCode: string } }
) {
  try {
    const { linkCode } = params
    
    // Extract tracking information from request
    const ipAddress = request.ip || 
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      'unknown'
    
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const referrer = request.headers.get('referer') || request.headers.get('referrer')
    
    // Track the click
    const result = await trackClick({
      linkCode,
      ipAddress,
      userAgent,
      referrer,
    })
    
    if (!result) {
      return NextResponse.json(
        { error: 'Invalid or inactive affiliate link' },
        { status: 404 }
      )
    }
    
    // Redirect to the original URL
    return NextResponse.redirect(result.redirectUrl, 302)
    
  } catch (error) {
    console.error('Affiliate link tracking error:', error)
    
    // In case of error, redirect to a fallback page or return error
    return NextResponse.json(
      { error: 'Link tracking failed' },
      { status: 500 }
    )
  }
}
