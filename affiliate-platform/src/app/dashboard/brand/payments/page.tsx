'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'

interface Commission {
  id: string
  amount: number
  currency: string
  status: 'PENDING' | 'APPROVED' | 'PAID' | 'CANCELLED'
  createdAt: string
  paidAt?: string
  affiliate: {
    id: string
    user: {
      name: string
      email: string
    }
    paymentInfo?: any
  }
  conversion: {
    id: string
    orderId?: string
    orderValue: number
    affiliateLink: {
      offer: {
        title: string
      }
    }
  }
}

export default function BrandPaymentsPage() {
  const [commissions, setCommissions] = useState<Commission[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCommissions, setSelectedCommissions] = useState<string[]>([])
  const [processing, setProcessing] = useState(false)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'paid'>('pending')

  useEffect(() => {
    fetchCommissions()
  }, [filter])

  const fetchCommissions = async () => {
    try {
      const token = localStorage.getItem('token')
      const params = new URLSearchParams()
      if (filter !== 'all') params.append('status', filter.toUpperCase())
      
      // Mock data for now - in real implementation, fetch from API
      const mockCommissions: Commission[] = [
        {
          id: '1',
          amount: 25.50,
          currency: 'USD',
          status: 'APPROVED',
          createdAt: '2024-06-24T10:00:00Z',
          affiliate: {
            id: 'aff1',
            user: {
              name: 'John Doe',
              email: '<EMAIL>'
            },
            paymentInfo: {
              stripeAccountId: 'acct_123',
              accountStatus: 'active'
            }
          },
          conversion: {
            id: 'conv1',
            orderId: 'order_123',
            orderValue: 127.50,
            affiliateLink: {
              offer: {
                title: 'Summer Sale 2024'
              }
            }
          }
        },
        {
          id: '2',
          amount: 15.75,
          currency: 'USD',
          status: 'APPROVED',
          createdAt: '2024-06-24T09:30:00Z',
          affiliate: {
            id: 'aff2',
            user: {
              name: 'Jane Smith',
              email: '<EMAIL>'
            },
            paymentInfo: {
              stripeAccountId: 'acct_456',
              accountStatus: 'active'
            }
          },
          conversion: {
            id: 'conv2',
            orderId: 'order_124',
            orderValue: 78.75,
            affiliateLink: {
              offer: {
                title: 'Tech Gadgets Discount'
              }
            }
          }
        },
        {
          id: '3',
          amount: 32.00,
          currency: 'USD',
          status: 'PAID',
          createdAt: '2024-06-23T14:00:00Z',
          paidAt: '2024-06-24T08:00:00Z',
          affiliate: {
            id: 'aff1',
            user: {
              name: 'John Doe',
              email: '<EMAIL>'
            }
          },
          conversion: {
            id: 'conv3',
            orderId: 'order_122',
            orderValue: 160.00,
            affiliateLink: {
              offer: {
                title: 'Fashion Week Special'
              }
            }
          }
        }
      ]

      const filteredCommissions = filter === 'all' 
        ? mockCommissions 
        : mockCommissions.filter(c => c.status.toLowerCase() === filter)
      
      setCommissions(filteredCommissions)
    } catch (error) {
      console.error('Error fetching commissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSelectCommission = (commissionId: string) => {
    setSelectedCommissions(prev => 
      prev.includes(commissionId)
        ? prev.filter(id => id !== commissionId)
        : [...prev, commissionId]
    )
  }

  const handleSelectAll = () => {
    const eligibleCommissions = commissions
      .filter(c => c.status === 'APPROVED' && c.affiliate.paymentInfo?.accountStatus === 'active')
      .map(c => c.id)
    
    setSelectedCommissions(
      selectedCommissions.length === eligibleCommissions.length ? [] : eligibleCommissions
    )
  }

  const processPayouts = async () => {
    if (selectedCommissions.length === 0) return
    
    setProcessing(true)
    
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/payments/payouts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          commissionIds: selectedCommissions
        })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        alert(`Successfully processed ${data.results.filter((r: any) => r.success).length} payouts`)
        setSelectedCommissions([])
        fetchCommissions()
      } else {
        alert('Error processing payouts: ' + data.error)
      }
    } catch (error) {
      console.error('Error processing payouts:', error)
      alert('Error processing payouts. Please try again.')
    } finally {
      setProcessing(false)
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED': return 'bg-green-100 text-green-800'
      case 'PAID': return 'bg-blue-100 text-blue-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const eligibleForPayout = commissions.filter(c => 
    c.status === 'APPROVED' && c.affiliate.paymentInfo?.accountStatus === 'active'
  )

  const totalSelectedAmount = commissions
    .filter(c => selectedCommissions.includes(c.id))
    .reduce((sum, c) => sum + c.amount, 0)

  if (loading) {
    return (
      <DashboardLayout title="Commission Payments">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Commission Payments">
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">⏳</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Approval</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {commissions.filter(c => c.status === 'PENDING').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">✅</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Ready to Pay</dt>
                    <dd className="text-lg font-medium text-gray-900">{eligibleForPayout.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">💳</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Paid This Month</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {commissions.filter(c => c.status === 'PAID').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">💰</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Paid</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatCurrency(
                        commissions.filter(c => c.status === 'PAID').reduce((sum, c) => sum + c.amount, 0),
                        'USD'
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex space-x-4">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="all">All Commissions</option>
                <option value="pending">Pending Approval</option>
                <option value="approved">Ready to Pay</option>
                <option value="paid">Paid</option>
              </select>
            </div>

            {eligibleForPayout.length > 0 && (
              <div className="flex items-center space-x-4">
                {selectedCommissions.length > 0 && (
                  <span className="text-sm text-gray-600">
                    {selectedCommissions.length} selected ({formatCurrency(totalSelectedAmount, 'USD')})
                  </span>
                )}
                <button
                  onClick={processPayouts}
                  disabled={selectedCommissions.length === 0 || processing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Process Payouts'
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Commissions Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Commissions</h3>
              {eligibleForPayout.length > 0 && (
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-indigo-600 hover:text-indigo-900"
                >
                  {selectedCommissions.length === eligibleForPayout.length ? 'Deselect All' : 'Select All Eligible'}
                </button>
              )}
            </div>
          </div>
          
          {commissions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Select
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Affiliate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Offer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Commission
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {commissions.map((commission) => {
                    const isEligible = commission.status === 'APPROVED' && 
                                     commission.affiliate.paymentInfo?.accountStatus === 'active'
                    
                    return (
                      <tr key={commission.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {isEligible && (
                            <input
                              type="checkbox"
                              checked={selectedCommissions.includes(commission.id)}
                              onChange={() => handleSelectCommission(commission.id)}
                              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                            />
                          )}
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {commission.affiliate.user.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {commission.affiliate.user.email}
                            </div>
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {commission.conversion.affiliateLink.offer.title}
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {commission.conversion.orderId || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatCurrency(commission.conversion.orderValue, commission.currency)}
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatCurrency(commission.amount, commission.currency)}
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(commission.status)}`}>
                            {commission.status}
                          </span>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>{formatDate(commission.createdAt)}</div>
                          {commission.paidAt && (
                            <div className="text-xs text-green-600">
                              Paid: {formatDate(commission.paidAt)}
                            </div>
                          )}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">💳</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No commissions found</h3>
              <p className="text-gray-500">
                {filter === 'all' 
                  ? 'No commissions have been generated yet.'
                  : `No ${filter} commissions found.`}
              </p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
