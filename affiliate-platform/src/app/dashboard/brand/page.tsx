'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import Link from 'next/link'

interface DashboardStats {
  totalOffers: number
  activeOffers: number
  totalAffiliates: number
  totalClicks: number
  totalConversions: number
  totalCommissionsPaid: number
  conversionRate: number
  averageOrderValue: number
}

interface RecentActivity {
  id: string
  type: 'offer_created' | 'affiliate_joined' | 'conversion' | 'commission_paid'
  description: string
  timestamp: string
  amount?: number
}

export default function BrandDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalOffers: 0,
    activeOffers: 0,
    totalAffiliates: 0,
    totalClicks: 0,
    totalConversions: 0,
    totalCommissionsPaid: 0,
    conversionRate: 0,
    averageOrderValue: 0
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      // For now, we'll use mock data since we haven't implemented the analytics API yet
      // In a real implementation, you would fetch from /api/dashboard/brand/stats
      
      // Mock data
      setStats({
        totalOffers: 12,
        activeOffers: 8,
        totalAffiliates: 45,
        totalClicks: 2847,
        totalConversions: 156,
        totalCommissionsPaid: 2340.50,
        conversionRate: 5.48,
        averageOrderValue: 89.32
      })

      setRecentActivity([
        {
          id: '1',
          type: 'conversion',
          description: 'New conversion from affiliate link',
          timestamp: '2024-06-24T10:30:00Z',
          amount: 15.50
        },
        {
          id: '2',
          type: 'affiliate_joined',
          description: 'New affiliate joined your program',
          timestamp: '2024-06-24T09:15:00Z'
        },
        {
          id: '3',
          type: 'offer_created',
          description: 'Summer Sale offer was created',
          timestamp: '2024-06-24T08:00:00Z'
        }
      ])

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'conversion': return '💰'
      case 'affiliate_joined': return '👤'
      case 'offer_created': return '🎯'
      case 'commission_paid': return '💳'
      default: return '📊'
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Brand Dashboard">
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">🎯</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Offers</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.activeOffers}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">👥</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Affiliates</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalAffiliates}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">👆</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalClicks.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">💰</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Conversions</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalConversions}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Performance Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Conversion Rate</span>
                  <span className="text-sm font-medium text-gray-900">{stats.conversionRate}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Average Order Value</span>
                  <span className="text-sm font-medium text-gray-900">{formatCurrency(stats.averageOrderValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total Commissions Paid</span>
                  <span className="text-sm font-medium text-gray-900">{formatCurrency(stats.totalCommissionsPaid)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <span className="text-lg">{getActivityIcon(activity.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">{formatDate(activity.timestamp)}</p>
                    </div>
                    {activity.amount && (
                      <span className="text-sm font-medium text-green-600">
                        +{formatCurrency(activity.amount)}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-5">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Link
                href="/dashboard/brand/offers/new"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-600 ring-4 ring-white">
                    <span className="text-xl">🎯</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Create Offer
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Launch a new promotional offer for affiliates
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/brand/affiliates"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-600 ring-4 ring-white">
                    <span className="text-xl">👥</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Manage Affiliates
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    View and manage your affiliate partners
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/brand/analytics"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-600 ring-4 ring-white">
                    <span className="text-xl">📈</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    View Analytics
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Detailed performance analytics and reports
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/brand/payments"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-600 ring-4 ring-white">
                    <span className="text-xl">💳</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Manage Payments
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Process commission payments to affiliates
                  </p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
