'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import DashboardLayout from '@/components/DashboardLayout'

interface PaymentAccount {
  hasAccount: boolean
  accountId?: string
  accountStatus: 'not_created' | 'pending' | 'active'
  chargesEnabled?: boolean
  detailsSubmitted?: boolean
  country?: string
  email?: string
}

export default function AffiliateSettingsPage() {
  const [paymentAccount, setPaymentAccount] = useState<PaymentAccount>({
    hasAccount: false,
    accountStatus: 'not_created'
  })
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const searchParams = useSearchParams()

  useEffect(() => {
    fetchPaymentAccount()
    
    // Handle setup completion
    const setup = searchParams.get('setup')
    if (setup === 'complete') {
      // Refresh account status after setup completion
      setTimeout(() => {
        fetchPaymentAccount()
      }, 2000)
    }
  }, [searchParams])

  const fetchPaymentAccount = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/payments/connect', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setPaymentAccount(data)
      } else {
        console.error('Error fetching payment account:', data.error)
      }
    } catch (error) {
      console.error('Error fetching payment account:', error)
    } finally {
      setLoading(false)
    }
  }

  const createPaymentAccount = async () => {
    setCreating(true)
    
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/payments/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          country: 'US', // You might want to make this configurable
          businessType: 'individual'
        })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        // Redirect to Stripe onboarding
        window.location.href = data.onboardingUrl
      } else {
        alert('Error creating payment account: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating payment account:', error)
      alert('Error creating payment account. Please try again.')
    } finally {
      setCreating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Active'
      case 'pending': return 'Setup Required'
      case 'not_created': return 'Not Set Up'
      default: return 'Unknown'
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Settings">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Settings">
      <div className="space-y-6">
        {/* Payment Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Payment Settings</h3>
            <p className="mt-1 text-sm text-gray-500">
              Manage your payment account to receive commission payouts
            </p>
          </div>
          
          <div className="p-6">
            <div className="space-y-6">
              {/* Account Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Account Status
                </label>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(paymentAccount.accountStatus)}`}>
                    {getStatusText(paymentAccount.accountStatus)}
                  </span>
                  {paymentAccount.accountStatus === 'active' && (
                    <span className="text-sm text-gray-500">✓ Ready to receive payments</span>
                  )}
                </div>
              </div>

              {/* Account Details */}
              {paymentAccount.hasAccount && (
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Account ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{paymentAccount.accountId}</p>
                  </div>
                  
                  {paymentAccount.country && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Country</label>
                      <p className="mt-1 text-sm text-gray-900">{paymentAccount.country.toUpperCase()}</p>
                    </div>
                  )}
                  
                  {paymentAccount.email && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <p className="mt-1 text-sm text-gray-900">{paymentAccount.email}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Charges Enabled</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {paymentAccount.chargesEnabled ? '✓ Yes' : '✗ No'}
                    </p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                {!paymentAccount.hasAccount ? (
                  <button
                    onClick={createPaymentAccount}
                    disabled={creating}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {creating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Setting up...
                      </>
                    ) : (
                      'Set Up Payment Account'
                    )}
                  </button>
                ) : paymentAccount.accountStatus === 'pending' ? (
                  <button
                    onClick={createPaymentAccount}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                  >
                    Complete Setup
                  </button>
                ) : (
                  <button
                    onClick={fetchPaymentAccount}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Refresh Status
                  </button>
                )}
              </div>

              {/* Information Box */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <span className="text-blue-400 text-xl">ℹ️</span>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      About Payment Setup
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>You need to complete payment setup to receive commission payouts</li>
                        <li>We use Stripe to securely process payments</li>
                        <li>You'll need to provide tax information and bank account details</li>
                        <li>Setup typically takes 2-3 minutes to complete</li>
                        <li>Payouts are processed weekly for active affiliates</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Profile Settings</h3>
            <p className="mt-1 text-sm text-gray-500">
              Update your affiliate profile information
            </p>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="displayName" className="block text-sm font-medium text-gray-700">
                  Display Name
                </label>
                <input
                  type="text"
                  id="displayName"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Your display name"
                />
              </div>
              
              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700">
                  Website
                </label>
                <input
                  type="url"
                  id="website"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="https://yourwebsite.com"
                />
              </div>
              
              <div className="sm:col-span-2">
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                  Bio
                </label>
                <textarea
                  id="bio"
                  rows={3}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Tell us about yourself and your audience..."
                />
              </div>
            </div>
            
            <div className="mt-6">
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
