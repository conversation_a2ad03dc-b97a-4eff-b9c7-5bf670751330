'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import Link from 'next/link'

interface DashboardStats {
  totalLinks: number
  activeLinks: number
  totalClicks: number
  totalConversions: number
  totalEarnings: number
  pendingEarnings: number
  conversionRate: number
  averageCommission: number
}

interface TopPerformingLink {
  id: string
  linkCode: string
  offerTitle: string
  brandName: string
  clicks: number
  conversions: number
  earnings: number
  conversionRate: number
}

interface RecentActivity {
  id: string
  type: 'click' | 'conversion' | 'commission_earned' | 'payment_received'
  description: string
  timestamp: string
  amount?: number
}

export default function AffiliateDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalLinks: 0,
    activeLinks: 0,
    totalClicks: 0,
    totalConversions: 0,
    totalEarnings: 0,
    pendingEarnings: 0,
    conversionRate: 0,
    averageCommission: 0
  })
  const [topLinks, setTopLinks] = useState<TopPerformingLink[]>([])
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      // Mock data for now - in real implementation, fetch from API
      setStats({
        totalLinks: 8,
        activeLinks: 6,
        totalClicks: 1247,
        totalConversions: 67,
        totalEarnings: 1834.50,
        pendingEarnings: 234.75,
        conversionRate: 5.37,
        averageCommission: 27.38
      })

      setTopLinks([
        {
          id: '1',
          linkCode: 'abc123',
          offerTitle: 'Summer Fashion Sale',
          brandName: 'Fashion Store',
          clicks: 456,
          conversions: 23,
          earnings: 345.50,
          conversionRate: 5.04
        },
        {
          id: '2',
          linkCode: 'def456',
          offerTitle: 'Tech Gadgets 20% Off',
          brandName: 'TechWorld',
          clicks: 321,
          conversions: 18,
          earnings: 270.00,
          conversionRate: 5.61
        },
        {
          id: '3',
          linkCode: 'ghi789',
          offerTitle: 'Home & Garden Deals',
          brandName: 'HomeDepot',
          clicks: 234,
          conversions: 12,
          earnings: 180.25,
          conversionRate: 5.13
        }
      ])

      setRecentActivity([
        {
          id: '1',
          type: 'conversion',
          description: 'Conversion from Summer Fashion Sale',
          timestamp: '2024-06-24T11:30:00Z',
          amount: 15.50
        },
        {
          id: '2',
          type: 'click',
          description: 'New click on Tech Gadgets offer',
          timestamp: '2024-06-24T10:45:00Z'
        },
        {
          id: '3',
          type: 'commission_earned',
          description: 'Commission earned from Home & Garden',
          timestamp: '2024-06-24T09:20:00Z',
          amount: 22.75
        },
        {
          id: '4',
          type: 'payment_received',
          description: 'Monthly commission payment received',
          timestamp: '2024-06-23T14:00:00Z',
          amount: 450.00
        }
      ])

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'click': return '👆'
      case 'conversion': return '💰'
      case 'commission_earned': return '💵'
      case 'payment_received': return '💳'
      default: return '📊'
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Affiliate Dashboard">
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">🔗</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Links</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.activeLinks}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">👆</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.totalClicks.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">💰</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Earnings</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.totalEarnings)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">⏳</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Earnings</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.pendingEarnings)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Overview */}
        <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Performance Overview</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Conversion Rate</span>
                  <span className="text-sm font-medium text-gray-900">{stats.conversionRate}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Average Commission</span>
                  <span className="text-sm font-medium text-gray-900">{formatCurrency(stats.averageCommission)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total Conversions</span>
                  <span className="text-sm font-medium text-gray-900">{stats.totalConversions}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {recentActivity.slice(0, 4).map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <span className="text-lg">{getActivityIcon(activity.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">{formatDate(activity.timestamp)}</p>
                    </div>
                    {activity.amount && (
                      <span className="text-sm font-medium text-green-600">
                        +{formatCurrency(activity.amount)}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Top Performing Links */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-5">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Top Performing Links</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Offer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Clicks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Conversions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Earnings
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {topLinks.map((link) => (
                    <tr key={link.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{link.offerTitle}</div>
                          <div className="text-sm text-gray-500">{link.brandName}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {link.clicks.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {link.conversions}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {link.conversionRate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        {formatCurrency(link.earnings)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-5">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Link
                href="/dashboard/affiliate/offers"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-600 ring-4 ring-white">
                    <span className="text-xl">🎯</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Browse Offers
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Find new offers to promote
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/affiliate/links"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-600 ring-4 ring-white">
                    <span className="text-xl">🔗</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    My Links
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Manage your affiliate links
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/affiliate/earnings"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-600 ring-4 ring-white">
                    <span className="text-xl">💰</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    View Earnings
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Track your commissions and payments
                  </p>
                </div>
              </Link>

              <Link
                href="/dashboard/affiliate/analytics"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-600 ring-4 ring-white">
                    <span className="text-xl">📈</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Analytics
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Detailed performance analytics
                  </p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
