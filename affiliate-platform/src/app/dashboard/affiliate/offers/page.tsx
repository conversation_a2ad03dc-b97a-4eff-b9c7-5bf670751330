'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'

interface Offer {
  id: string
  title: string
  description: string
  offerType: string
  discountValue?: number
  discountType?: string
  commissionRate: number
  commissionType: string
  category?: string
  tags: string[]
  imageUrl?: string
  startDate?: string
  endDate?: string
  status: string
  brand: {
    id: string
    companyName: string
    logo?: string
  }
  _count: {
    affiliateLinks: number
  }
}

export default function AffiliateOffersPage() {
  const [offers, setOffers] = useState<Offer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [generatingLink, setGeneratingLink] = useState<string | null>(null)

  useEffect(() => {
    fetchOffers()
  }, [searchTerm, selectedCategory])

  const fetchOffers = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (selectedCategory) params.append('category', selectedCategory)
      
      const response = await fetch(`/api/offers?${params}`)
      const data = await response.json()
      
      if (response.ok) {
        setOffers(data.offers)
      } else {
        console.error('Error fetching offers:', data.error)
      }
    } catch (error) {
      console.error('Error fetching offers:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateAffiliateLink = async (offer: Offer) => {
    setGeneratingLink(offer.id)
    
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/affiliate-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          offerId: offer.id,
          originalUrl: `https://${offer.brand.companyName.toLowerCase().replace(/\s+/g, '')}.com/offer/${offer.id}`
        })
      })

      const data = await response.json()
      
      if (response.ok) {
        // Copy link to clipboard
        await navigator.clipboard.writeText(data.link.affiliateUrl)
        alert(`Affiliate link generated and copied to clipboard!\n\n${data.link.affiliateUrl}`)
      } else {
        alert('Error generating affiliate link: ' + data.error)
      }
    } catch (error) {
      console.error('Error generating affiliate link:', error)
      alert('Error generating affiliate link. Please try again.')
    } finally {
      setGeneratingLink(null)
    }
  }

  const formatCommission = (rate: number, type: string) => {
    if (type === 'PERCENTAGE') {
      return `${rate}%`
    } else {
      return `$${rate}`
    }
  }

  const getOfferTypeDisplay = (type: string) => {
    switch (type) {
      case 'DISCOUNT': return '💸 Discount'
      case 'CASHBACK': return '💰 Cashback'
      case 'FREE_SHIPPING': return '🚚 Free Shipping'
      case 'BOGO': return '🎁 Buy One Get One'
      default: return '🎯 Special Offer'
    }
  }

  const categories = ['Fashion', 'Technology', 'Home & Garden', 'Health & Beauty', 'Sports', 'Travel']

  if (loading) {
    return (
      <DashboardLayout title="Browse Offers">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Browse Offers">
      <div className="space-y-6">
        {/* Search and Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                Search Offers
              </label>
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by title, description, or brand..."
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('')
                }}
                className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Offers Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {offers.map((offer) => (
            <div key={offer.id} className="bg-white overflow-hidden shadow rounded-lg">
              {offer.imageUrl && (
                <div className="h-48 bg-gray-200">
                  <img
                    className="h-full w-full object-cover"
                    src={offer.imageUrl}
                    alt={offer.title}
                  />
                </div>
              )}
              
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-500">{offer.brand.companyName}</span>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    {getOfferTypeDisplay(offer.offerType)}
                  </span>
                </div>
                
                <h3 className="text-lg font-medium text-gray-900 mb-2">{offer.title}</h3>
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">{offer.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Commission:</span>
                    <span className="font-medium text-green-600">
                      {formatCommission(offer.commissionRate, offer.commissionType)}
                    </span>
                  </div>
                  
                  {offer.category && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Category:</span>
                      <span className="text-gray-900">{offer.category}</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Active Affiliates:</span>
                    <span className="text-gray-900">{offer._count.affiliateLinks}</span>
                  </div>
                </div>

                {offer.tags.length > 0 && (
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {offer.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                      {offer.tags.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          +{offer.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <button
                  onClick={() => generateAffiliateLink(offer)}
                  disabled={generatingLink === offer.id}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {generatingLink === offer.id ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </>
                  ) : (
                    'Generate Affiliate Link'
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        {offers.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🎯</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No offers found</h3>
            <p className="text-gray-500">
              {searchTerm || selectedCategory
                ? 'Try adjusting your search criteria or filters.'
                : 'No active offers are currently available.'}
            </p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
