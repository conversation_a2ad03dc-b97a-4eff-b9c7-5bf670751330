'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'

interface AffiliateLink {
  id: string
  linkCode: string
  originalUrl: string
  affiliateUrl: string
  clicks: number
  conversionCount: number
  isActive: boolean
  createdAt: string
  offer: {
    id: string
    title: string
    commissionRate: number
    commissionType: string
    brand: {
      companyName: string
    }
  }
}

export default function AffiliateLinksPage() {
  const [links, setLinks] = useState<AffiliateLink[]>([])
  const [loading, setLoading] = useState(true)
  const [copiedLink, setCopiedLink] = useState<string | null>(null)

  useEffect(() => {
    fetchAffiliateLinks()
  }, [])

  const fetchAffiliateLinks = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/affiliate-links', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setLinks(data.links)
      } else {
        console.error('Error fetching affiliate links:', data.error)
      }
    } catch (error) {
      console.error('Error fetching affiliate links:', error)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (url: string, linkId: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopiedLink(linkId)
      setTimeout(() => setCopiedLink(null), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatCommission = (rate: number, type: string) => {
    if (type === 'PERCENTAGE') {
      return `${rate}%`
    } else {
      return `$${rate}`
    }
  }

  const calculateConversionRate = (conversions: number, clicks: number) => {
    if (clicks === 0) return '0.00'
    return ((conversions / clicks) * 100).toFixed(2)
  }

  const getPerformanceColor = (conversionRate: number) => {
    if (conversionRate >= 5) return 'text-green-600'
    if (conversionRate >= 2) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <DashboardLayout title="My Affiliate Links">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="My Affiliate Links">
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">🔗</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Links</dt>
                    <dd className="text-lg font-medium text-gray-900">{links.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">👆</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {links.reduce((sum, link) => sum + link.clicks, 0).toLocaleString()}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">💰</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Conversions</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {links.reduce((sum, link) => sum + link.conversionCount, 0)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <span className="text-2xl">📈</span>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg. Rate</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {links.length > 0 
                        ? calculateConversionRate(
                            links.reduce((sum, link) => sum + link.conversionCount, 0),
                            links.reduce((sum, link) => sum + link.clicks, 0)
                          )
                        : '0.00'
                      }%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Links Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Your Affiliate Links</h3>
            <p className="mt-1 text-sm text-gray-500">
              Manage and track performance of your affiliate links
            </p>
          </div>
          
          {links.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Offer Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Commission
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {links.map((link) => {
                    const conversionRate = parseFloat(calculateConversionRate(link.conversionCount, link.clicks))
                    
                    return (
                      <tr key={link.id} className={!link.isActive ? 'opacity-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{link.offer.title}</div>
                            <div className="text-sm text-gray-500">{link.offer.brand.companyName}</div>
                            <div className="text-xs text-gray-400 mt-1">
                              Code: {link.linkCode}
                            </div>
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <div>Clicks: {link.clicks.toLocaleString()}</div>
                            <div>Conversions: {link.conversionCount}</div>
                            <div className={`font-medium ${getPerformanceColor(conversionRate)}`}>
                              Rate: {conversionRate}%
                            </div>
                          </div>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-green-600">
                            {formatCommission(link.offer.commissionRate, link.offer.commissionType)}
                          </span>
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(link.createdAt)}
                        </td>
                        
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => copyToClipboard(link.affiliateUrl, link.id)}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              {copiedLink === link.id ? 'Copied!' : 'Copy Link'}
                            </button>
                            <span className="text-gray-300">|</span>
                            <a
                              href={link.affiliateUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-gray-600 hover:text-gray-900"
                            >
                              Test
                            </a>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🔗</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No affiliate links yet</h3>
              <p className="text-gray-500 mb-6">
                Start by browsing offers and generating your first affiliate link.
              </p>
              <a
                href="/dashboard/affiliate/offers"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Browse Offers
              </a>
            </div>
          )}
        </div>

        {/* Tips Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">💡 Tips for Better Performance</h3>
          <ul className="space-y-2 text-sm text-blue-800">
            <li>• Share your affiliate links on relevant social media platforms</li>
            <li>• Write compelling content that naturally incorporates your links</li>
            <li>• Track which platforms and content types perform best</li>
            <li>• Test different offers to find what resonates with your audience</li>
            <li>• Be transparent about affiliate relationships to build trust</li>
          </ul>
        </div>
      </div>
    </DashboardLayout>
  )
}
