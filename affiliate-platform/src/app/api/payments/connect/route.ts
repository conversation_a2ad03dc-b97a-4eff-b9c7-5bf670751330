import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { validateSession } from '@/lib/auth'
import { stripeService } from '@/lib/stripe'

const connectAccountSchema = z.object({
  country: z.string().min(2).max(2), // ISO country code
  businessType: z.enum(['individual', 'company']).optional(),
})

// POST /api/payments/connect - Create Stripe Connect account for affiliate
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'AFFILIATE') {
      return NextResponse.json(
        { error: 'Unauthorized - Affiliates only' },
        { status: 401 }
      )
    }
    
    const affiliate = await prisma.affiliate.findUnique({
      where: { userId: session.userId },
      include: { user: true },
    })
    
    if (!affiliate) {
      return NextResponse.json(
        { error: 'Affiliate profile not found' },
        { status: 404 }
      )
    }
    
    // Check if affiliate already has a Stripe account
    const existingPaymentInfo = affiliate.paymentInfo as any
    if (existingPaymentInfo?.stripeAccountId) {
      return NextResponse.json(
        { error: 'Payment account already exists' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const { country, businessType } = connectAccountSchema.parse(body)
    
    // Create Stripe Connect account
    const stripeAccount = await stripeService.createConnectedAccount({
      email: affiliate.user.email,
      country,
      type: 'express',
      businessType,
    })
    
    // Create account onboarding link
    const baseUrl = process.env.APP_URL || 'http://localhost:3000'
    const accountLink = await stripeService.createAccountLink(
      stripeAccount.id,
      `${baseUrl}/dashboard/affiliate/settings?setup=refresh`,
      `${baseUrl}/dashboard/affiliate/settings?setup=complete`
    )
    
    // Update affiliate payment info
    await prisma.affiliate.update({
      where: { id: affiliate.id },
      data: {
        paymentInfo: {
          stripeAccountId: stripeAccount.id,
          accountStatus: 'pending',
          onboardingUrl: accountLink.url,
          country,
          businessType,
        },
      },
    })
    
    return NextResponse.json({
      message: 'Payment account created successfully',
      accountId: stripeAccount.id,
      onboardingUrl: accountLink.url,
    })
    
  } catch (error) {
    console.error('Connect account creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/payments/connect - Get payment account status
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'AFFILIATE') {
      return NextResponse.json(
        { error: 'Unauthorized - Affiliates only' },
        { status: 401 }
      )
    }
    
    const affiliate = await prisma.affiliate.findUnique({
      where: { userId: session.userId },
    })
    
    if (!affiliate) {
      return NextResponse.json(
        { error: 'Affiliate profile not found' },
        { status: 404 }
      )
    }
    
    const paymentInfo = affiliate.paymentInfo as any
    
    if (!paymentInfo?.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        accountStatus: 'not_created',
      })
    }
    
    // Get account details from Stripe
    const stripeAccount = await stripeService.getAccountDetails(paymentInfo.stripeAccountId)
    
    const accountStatus = stripeAccount.details_submitted && stripeAccount.charges_enabled
      ? 'active'
      : 'pending'
    
    // Update local status
    await prisma.affiliate.update({
      where: { id: affiliate.id },
      data: {
        paymentInfo: {
          ...paymentInfo,
          accountStatus,
          chargesEnabled: stripeAccount.charges_enabled,
          detailsSubmitted: stripeAccount.details_submitted,
        },
      },
    })
    
    return NextResponse.json({
      hasAccount: true,
      accountId: stripeAccount.id,
      accountStatus,
      chargesEnabled: stripeAccount.charges_enabled,
      detailsSubmitted: stripeAccount.details_submitted,
      country: stripeAccount.country,
      email: stripeAccount.email,
    })
    
  } catch (error) {
    console.error('Get connect account error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
