import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { validateSession } from '@/lib/auth'
import { stripeService } from '@/lib/stripe'

const createPayoutSchema = z.object({
  commissionIds: z.array(z.string()).min(1),
})

// POST /api/payments/payouts - Process commission payouts (brands only)
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'BRAND') {
      return NextResponse.json(
        { error: 'Unauthorized - Brands only' },
        { status: 401 }
      )
    }
    
    const brand = await prisma.brand.findUnique({
      where: { userId: session.userId },
    })
    
    if (!brand) {
      return NextResponse.json(
        { error: 'Brand profile not found' },
        { status: 404 }
      )
    }
    
    const body = await request.json()
    const { commissionIds } = createPayoutSchema.parse(body)
    
    // Get commissions with affiliate payment info
    const commissions = await prisma.commission.findMany({
      where: {
        id: { in: commissionIds },
        status: 'APPROVED',
        affiliate: {
          brand: {
            // Only allow brands to pay their own affiliates' commissions
            offers: {
              some: {
                brandId: brand.id,
              },
            },
          },
        },
      },
      include: {
        affiliate: {
          include: {
            user: true,
          },
        },
        conversion: {
          include: {
            affiliateLink: {
              include: {
                offer: true,
              },
            },
          },
        },
      },
    })
    
    if (commissions.length === 0) {
      return NextResponse.json(
        { error: 'No eligible commissions found' },
        { status: 404 }
      )
    }
    
    // Group commissions by affiliate
    const commissionsByAffiliate = commissions.reduce((acc, commission) => {
      const affiliateId = commission.affiliateId
      if (!acc[affiliateId]) {
        acc[affiliateId] = []
      }
      acc[affiliateId].push(commission)
      return acc
    }, {} as Record<string, typeof commissions>)
    
    const payoutResults = []
    
    // Process payouts for each affiliate
    for (const [affiliateId, affiliateCommissions] of Object.entries(commissionsByAffiliate)) {
      const affiliate = affiliateCommissions[0].affiliate
      const paymentInfo = affiliate.paymentInfo as any
      
      if (!paymentInfo?.stripeAccountId || paymentInfo.accountStatus !== 'active') {
        payoutResults.push({
          affiliateId,
          success: false,
          error: 'Affiliate payment account not set up or not active',
          commissionIds: affiliateCommissions.map(c => c.id),
        })
        continue
      }
      
      const totalAmount = affiliateCommissions.reduce((sum, c) => sum + Number(c.amount), 0)
      
      try {
        // Create payout
        const payout = await stripeService.createPayout({
          amount: totalAmount,
          currency: affiliateCommissions[0].currency,
          destination: paymentInfo.stripeAccountId,
          description: `Commission payout for ${affiliateCommissions.length} conversions`,
          metadata: {
            brand_id: brand.id,
            affiliate_id: affiliateId,
            commission_count: affiliateCommissions.length.toString(),
          },
        })
        
        // Update commission statuses
        await prisma.commission.updateMany({
          where: {
            id: { in: affiliateCommissions.map(c => c.id) },
          },
          data: {
            status: 'PAID',
            paidAt: new Date(),
          },
        })
        
        // Update affiliate earnings
        await prisma.affiliate.update({
          where: { id: affiliateId },
          data: {
            pendingEarnings: {
              decrement: totalAmount,
            },
          },
        })
        
        payoutResults.push({
          affiliateId,
          success: true,
          payoutId: payout.id,
          amount: totalAmount,
          currency: affiliateCommissions[0].currency,
          commissionIds: affiliateCommissions.map(c => c.id),
        })
        
      } catch (error) {
        console.error(`Payout error for affiliate ${affiliateId}:`, error)
        
        payoutResults.push({
          affiliateId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          commissionIds: affiliateCommissions.map(c => c.id),
        })
      }
    }
    
    const successCount = payoutResults.filter(r => r.success).length
    const totalAmount = payoutResults
      .filter(r => r.success)
      .reduce((sum, r) => sum + (r.amount || 0), 0)
    
    return NextResponse.json({
      message: `Processed ${successCount} payouts successfully`,
      totalAmount,
      results: payoutResults,
    })
    
  } catch (error) {
    console.error('Payout processing error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/payments/payouts - Get payout history
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit
    
    let whereClause: any = {}
    
    if (session.user.role === 'AFFILIATE') {
      const affiliate = await prisma.affiliate.findUnique({
        where: { userId: session.userId },
      })
      
      if (!affiliate) {
        return NextResponse.json(
          { error: 'Affiliate profile not found' },
          { status: 404 }
        )
      }
      
      whereClause.affiliateId = affiliate.id
    } else if (session.user.role === 'BRAND') {
      const brand = await prisma.brand.findUnique({
        where: { userId: session.userId },
      })
      
      if (!brand) {
        return NextResponse.json(
          { error: 'Brand profile not found' },
          { status: 404 }
        )
      }
      
      whereClause.conversion = {
        affiliateLink: {
          offer: {
            brandId: brand.id,
          },
        },
      }
    }
    
    const [commissions, total] = await Promise.all([
      prisma.commission.findMany({
        where: {
          ...whereClause,
          status: 'PAID',
        },
        include: {
          affiliate: {
            include: {
              user: true,
            },
          },
          conversion: {
            include: {
              affiliateLink: {
                include: {
                  offer: {
                    include: {
                      brand: true,
                    },
                  },
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { paidAt: 'desc' },
      }),
      prisma.commission.count({
        where: {
          ...whereClause,
          status: 'PAID',
        },
      }),
    ])
    
    return NextResponse.json({
      payouts: commissions.map(commission => ({
        id: commission.id,
        amount: commission.amount,
        currency: commission.currency,
        paidAt: commission.paidAt,
        affiliate: {
          id: commission.affiliate.id,
          name: commission.affiliate.user.name,
          email: commission.affiliate.user.email,
        },
        offer: {
          id: commission.conversion.affiliateLink.offer.id,
          title: commission.conversion.affiliateLink.offer.title,
          brand: commission.conversion.affiliateLink.offer.brand.companyName,
        },
        conversion: {
          id: commission.conversion.id,
          orderValue: commission.conversion.orderValue,
          orderId: commission.conversion.orderId,
        },
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
    
  } catch (error) {
    console.error('Get payouts error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
