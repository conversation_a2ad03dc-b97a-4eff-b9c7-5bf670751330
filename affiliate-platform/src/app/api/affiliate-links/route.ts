import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { validateSession } from '@/lib/auth'
import { generateAffiliateLink, buildAffiliateUrl } from '@/lib/affiliate-links'

const createLinkSchema = z.object({
  offerId: z.string(),
  originalUrl: z.string().url(),
})

// GET /api/affiliate-links - Get affiliate's links
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'AFFILIATE') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const affiliate = await prisma.affiliate.findUnique({
      where: { userId: session.userId },
    })
    
    if (!affiliate) {
      return NextResponse.json(
        { error: 'Affiliate profile not found' },
        { status: 404 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit
    
    const [links, total] = await Promise.all([
      prisma.affiliateLink.findMany({
        where: { affiliateId: affiliate.id },
        include: {
          offer: {
            include: {
              brand: {
                select: {
                  id: true,
                  companyName: true,
                  logo: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.affiliateLink.count({
        where: { affiliateId: affiliate.id },
      }),
    ])
    
    // Add full affiliate URLs to response
    const linksWithUrls = links.map(link => ({
      ...link,
      affiliateUrl: buildAffiliateUrl(link.linkCode),
    }))
    
    return NextResponse.json({
      links: linksWithUrls,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
    
  } catch (error) {
    console.error('Get affiliate links error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/affiliate-links - Generate new affiliate link
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'AFFILIATE') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const affiliate = await prisma.affiliate.findUnique({
      where: { userId: session.userId },
    })
    
    if (!affiliate) {
      return NextResponse.json(
        { error: 'Affiliate profile not found' },
        { status: 404 }
      )
    }
    
    const body = await request.json()
    const { offerId, originalUrl } = createLinkSchema.parse(body)
    
    // Verify offer exists and is active
    const offer = await prisma.offer.findUnique({
      where: { id: offerId },
      include: {
        brand: true,
      },
    })
    
    if (!offer || offer.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Offer not found or not active' },
        { status: 404 }
      )
    }
    
    // Generate affiliate link
    const affiliateLink = await generateAffiliateLink({
      affiliateId: affiliate.id,
      offerId,
      originalUrl,
    })
    
    const response = {
      ...affiliateLink,
      affiliateUrl: buildAffiliateUrl(affiliateLink.linkCode),
    }
    
    return NextResponse.json({
      message: 'Affiliate link generated successfully',
      link: response,
    })
    
  } catch (error) {
    console.error('Create affiliate link error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
