import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { validateSession } from '@/lib/auth'

const createOfferSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
  terms: z.string().optional(),
  offerType: z.enum(['DISCOUNT', 'CASHBACK', 'FREE_SHIPPING', 'BOGO', 'CUSTOM']),
  discountValue: z.number().optional(),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  commissionRate: z.number().min(0).max(100),
  commissionType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).default('PERCENTAGE'),
  fixedCommission: z.number().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  imageUrl: z.string().url().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  maxCommissions: z.number().optional(),
})

// GET /api/offers - List offers (for affiliates to browse)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const status = searchParams.get('status') || 'ACTIVE'
    
    const skip = (page - 1) * limit
    
    const where: any = {
      status,
    }
    
    if (category) {
      where.category = category
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { has: search } },
      ]
    }
    
    const [offers, total] = await Promise.all([
      prisma.offer.findMany({
        where,
        include: {
          brand: {
            select: {
              id: true,
              companyName: true,
              logo: true,
            },
          },
          _count: {
            select: {
              affiliateLinks: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.offer.count({ where }),
    ])
    
    return NextResponse.json({
      offers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
    
  } catch (error) {
    console.error('Get offers error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/offers - Create new offer (brands only)
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const token = authHeader.substring(7)
    const session = await validateSession(token)
    
    if (!session || session.user.role !== 'BRAND') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    const brand = await prisma.brand.findUnique({
      where: { userId: session.userId },
    })
    
    if (!brand) {
      return NextResponse.json(
        { error: 'Brand profile not found' },
        { status: 404 }
      )
    }
    
    const body = await request.json()
    const validatedData = createOfferSchema.parse(body)
    
    const offer = await prisma.offer.create({
      data: {
        ...validatedData,
        brandId: brand.id,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
      },
      include: {
        brand: {
          select: {
            id: true,
            companyName: true,
            logo: true,
          },
        },
      },
    })
    
    return NextResponse.json({
      message: 'Offer created successfully',
      offer,
    })
    
  } catch (error) {
    console.error('Create offer error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
