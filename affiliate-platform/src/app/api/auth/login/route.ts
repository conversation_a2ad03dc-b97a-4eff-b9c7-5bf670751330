import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { verifyPassword, createSession } from '@/lib/auth'

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = loginSchema.parse(body)
    
    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        brand: true,
        affiliate: true,
      },
    })
    
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }
    
    // Verify password
    const isValidPassword = await verifyPassword(password, user.password)
    
    if (!isValidPassword) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }
    
    // Create session
    const token = await createSession(user.id)
    
    // Prepare user data based on role
    let userData: any = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      verified: user.verified,
    }
    
    if (user.role === 'BRAND' && user.brand) {
      userData.brand = {
        id: user.brand.id,
        companyName: user.brand.companyName,
        website: user.brand.website,
        status: user.brand.status,
      }
    } else if (user.role === 'AFFILIATE' && user.affiliate) {
      userData.affiliate = {
        id: user.affiliate.id,
        displayName: user.affiliate.displayName,
        status: user.affiliate.status,
        totalEarnings: user.affiliate.totalEarnings,
        pendingEarnings: user.affiliate.pendingEarnings,
      }
    }
    
    return NextResponse.json({
      message: 'Login successful',
      user: userData,
      token,
    })
    
  } catch (error) {
    console.error('Login error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
