import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { hashPassword, createSession } from '@/lib/auth'

const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().min(2),
  role: z.enum(['BRAND', 'AFFILIATE']),
  companyName: z.string().optional(), // Required for brands
  website: z.string().url().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)
    
    const { email, password, name, role, companyName, website } = validatedData
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      )
    }
    
    // Hash password
    const hashedPassword = await hashPassword(password)
    
    // Create user and related profile in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          role,
        },
      })
      
      // Create role-specific profile
      if (role === 'BRAND') {
        if (!companyName) {
          throw new Error('Company name is required for brands')
        }
        
        await tx.brand.create({
          data: {
            userId: user.id,
            companyName,
            website,
          },
        })
      } else if (role === 'AFFILIATE') {
        await tx.affiliate.create({
          data: {
            userId: user.id,
            displayName: name,
          },
        })
      }
      
      return user
    })
    
    // Create session
    const token = await createSession(result.id)
    
    return NextResponse.json({
      message: 'User registered successfully',
      user: {
        id: result.id,
        email: result.email,
        name: result.name,
        role: result.role,
      },
      token,
    })
    
  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
