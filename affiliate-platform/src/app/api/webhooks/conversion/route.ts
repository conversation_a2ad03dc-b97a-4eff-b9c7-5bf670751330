import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { recordConversion } from '@/lib/affiliate-links'

const conversionSchema = z.object({
  linkCode: z.string(),
  orderId: z.string().optional(),
  orderValue: z.number().positive(),
  currency: z.string().default('USD'),
  conversionData: z.any().optional(),
  // Add webhook signature verification fields
  signature: z.string().optional(),
  timestamp: z.string().optional(),
})

// POST /api/webhooks/conversion - Record conversion from e-commerce platforms
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = conversionSchema.parse(body)
    
    // TODO: Add webhook signature verification for security
    // This would verify that the webhook is coming from a trusted source
    // Each integration (Shopify, WooCommerce, etc.) would have its own verification method
    
    const { linkCode, orderId, orderValue, currency, conversionData } = validatedData
    
    // Record the conversion
    const result = await recordConversion({
      linkCode,
      orderId,
      orderValue,
      currency,
      conversionData,
    })
    
    return NextResponse.json({
      message: 'Conversion recorded successfully',
      conversion: result.conversion,
      commission: result.commission,
    })
    
  } catch (error) {
    console.error('Conversion webhook error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid webhook data', details: error.errors },
        { status: 400 }
      )
    }
    
    if (error instanceof Error && error.message === 'Affiliate link not found') {
      return NextResponse.json(
        { error: 'Invalid affiliate link' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/webhooks/conversion - Health check for webhook endpoint
export async function GET() {
  return NextResponse.json({
    message: 'Conversion webhook endpoint is active',
    timestamp: new Date().toISOString(),
  })
}
