import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { stripeService } from '@/lib/stripe'
import <PERSON><PERSON> from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')
    
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe signature' },
        { status: 400 }
      )
    }
    
    // Verify webhook signature
    const event = await stripeService.handleWebhook(body, signature)
    
    console.log(`Received Stripe webhook: ${event.type}`)
    
    // Handle different event types
    switch (event.type) {
      case 'account.updated':
        await handleAccountUpdated(event.data.object as Stripe.Account)
        break
        
      case 'payout.created':
        await handlePayoutCreated(event.data.object as Stripe.Payout)
        break
        
      case 'payout.paid':
        await handlePayoutPaid(event.data.object as Stripe.Payout)
        break
        
      case 'payout.failed':
        await handlePayoutFailed(event.data.object as Stripe.Payout)
        break
        
      case 'transfer.created':
        await handleTransferCreated(event.data.object as Stripe.Transfer)
        break
        
      case 'transfer.paid':
        await handleTransferPaid(event.data.object as Stripe.Transfer)
        break
        
      case 'transfer.failed':
        await handleTransferFailed(event.data.object as Stripe.Transfer)
        break
        
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }
    
    return NextResponse.json({ received: true })
    
  } catch (error) {
    console.error('Stripe webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 400 }
    )
  }
}

async function handleAccountUpdated(account: Stripe.Account) {
  try {
    // Find affiliate with this Stripe account
    const affiliate = await prisma.affiliate.findFirst({
      where: {
        paymentInfo: {
          path: ['stripeAccountId'],
          equals: account.id,
        },
      },
    })
    
    if (!affiliate) {
      console.log(`No affiliate found for Stripe account: ${account.id}`)
      return
    }
    
    const paymentInfo = affiliate.paymentInfo as any
    const accountStatus = account.details_submitted && account.charges_enabled
      ? 'active'
      : 'pending'
    
    // Update affiliate payment info
    await prisma.affiliate.update({
      where: { id: affiliate.id },
      data: {
        paymentInfo: {
          ...paymentInfo,
          accountStatus,
          chargesEnabled: account.charges_enabled,
          detailsSubmitted: account.details_submitted,
          updatedAt: new Date().toISOString(),
        },
      },
    })
    
    console.log(`Updated account status for affiliate ${affiliate.id}: ${accountStatus}`)
    
  } catch (error) {
    console.error('Error handling account.updated:', error)
  }
}

async function handlePayoutCreated(payout: Stripe.Payout) {
  try {
    console.log(`Payout created: ${payout.id} for ${payout.amount / 100} ${payout.currency}`)
    
    // Log payout creation - you might want to store this in a separate payouts table
    // For now, we'll just log it
    
  } catch (error) {
    console.error('Error handling payout.created:', error)
  }
}

async function handlePayoutPaid(payout: Stripe.Payout) {
  try {
    console.log(`Payout paid: ${payout.id}`)
    
    // If you have commission IDs in metadata, you can update their status here
    if (payout.metadata?.commission_ids) {
      const commissionIds = payout.metadata.commission_ids.split(',')
      
      await prisma.commission.updateMany({
        where: {
          id: { in: commissionIds },
        },
        data: {
          status: 'PAID',
          paidAt: new Date(payout.arrival_date * 1000),
        },
      })
      
      console.log(`Updated ${commissionIds.length} commissions to PAID status`)
    }
    
  } catch (error) {
    console.error('Error handling payout.paid:', error)
  }
}

async function handlePayoutFailed(payout: Stripe.Payout) {
  try {
    console.log(`Payout failed: ${payout.id}`)
    
    // If you have commission IDs in metadata, revert their status
    if (payout.metadata?.commission_ids) {
      const commissionIds = payout.metadata.commission_ids.split(',')
      
      await prisma.commission.updateMany({
        where: {
          id: { in: commissionIds },
        },
        data: {
          status: 'APPROVED', // Revert to approved so they can be retried
        },
      })
      
      // Also restore pending earnings
      if (payout.metadata?.affiliate_id) {
        await prisma.affiliate.update({
          where: { id: payout.metadata.affiliate_id },
          data: {
            pendingEarnings: {
              increment: payout.amount / 100, // Convert back from cents
            },
          },
        })
      }
      
      console.log(`Reverted ${commissionIds.length} commissions due to failed payout`)
    }
    
  } catch (error) {
    console.error('Error handling payout.failed:', error)
  }
}

async function handleTransferCreated(transfer: Stripe.Transfer) {
  try {
    console.log(`Transfer created: ${transfer.id} for ${transfer.amount / 100} ${transfer.currency}`)
    
  } catch (error) {
    console.error('Error handling transfer.created:', error)
  }
}

async function handleTransferPaid(transfer: Stripe.Transfer) {
  try {
    console.log(`Transfer paid: ${transfer.id}`)
    
  } catch (error) {
    console.error('Error handling transfer.paid:', error)
  }
}

async function handleTransferFailed(transfer: Stripe.Transfer) {
  try {
    console.log(`Transfer failed: ${transfer.id}`)
    
    // Handle transfer failure - similar to payout failure
    if (transfer.metadata?.commission_ids) {
      const commissionIds = transfer.metadata.commission_ids.split(',')
      
      await prisma.commission.updateMany({
        where: {
          id: { in: commissionIds },
        },
        data: {
          status: 'APPROVED',
        },
      })
      
      console.log(`Reverted ${commissionIds.length} commissions due to failed transfer`)
    }
    
  } catch (error) {
    console.error('Error handling transfer.failed:', error)
  }
}
