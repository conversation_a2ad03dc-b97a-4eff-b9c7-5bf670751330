import { nanoid } from 'nanoid'
import { prisma } from './prisma'

export interface CreateAffiliateLinkParams {
  affiliateId: string
  offerId: string
  originalUrl: string
}

export interface TrackClickParams {
  linkCode: string
  ipAddress?: string
  userAgent?: string
  referrer?: string
}

export async function generateAffiliateLink(params: CreateAffiliateLinkParams) {
  const { affiliateId, offerId, originalUrl } = params
  
  // Check if link already exists for this affiliate-offer combination
  const existingLink = await prisma.affiliateLink.findUnique({
    where: {
      affiliateId_offerId: {
        affiliateId,
        offerId,
      },
    },
  })
  
  if (existingLink) {
    return existingLink
  }
  
  // Generate unique link code
  const linkCode = nanoid(10)
  
  // Create new affiliate link
  const affiliateLink = await prisma.affiliateLink.create({
    data: {
      affiliateId,
      offerId,
      linkCode,
      originalUrl,
    },
    include: {
      offer: {
        include: {
          brand: true,
        },
      },
      affiliate: {
        include: {
          user: true,
        },
      },
    },
  })
  
  return affiliateLink
}

export async function getAffiliateLinkByCode(linkCode: string) {
  return prisma.affiliateLink.findUnique({
    where: { linkCode },
    include: {
      offer: {
        include: {
          brand: true,
        },
      },
      affiliate: {
        include: {
          user: true,
        },
      },
    },
  })
}

export async function trackClick(params: TrackClickParams) {
  const { linkCode, ipAddress, userAgent, referrer } = params
  
  const affiliateLink = await getAffiliateLinkByCode(linkCode)
  
  if (!affiliateLink || !affiliateLink.isActive) {
    return null
  }
  
  // Create click event
  const clickEvent = await prisma.clickEvent.create({
    data: {
      affiliateLinkId: affiliateLink.id,
      ipAddress,
      userAgent,
      referrer,
    },
  })
  
  // Update click count
  await prisma.affiliateLink.update({
    where: { id: affiliateLink.id },
    data: {
      clicks: {
        increment: 1,
      },
    },
  })
  
  return {
    clickEvent,
    redirectUrl: affiliateLink.originalUrl,
  }
}

export async function recordConversion(params: {
  linkCode: string
  orderId?: string
  orderValue: number
  currency?: string
  conversionData?: any
}) {
  const { linkCode, orderId, orderValue, currency = 'USD', conversionData } = params
  
  const affiliateLink = await getAffiliateLinkByCode(linkCode)
  
  if (!affiliateLink) {
    throw new Error('Affiliate link not found')
  }
  
  // Calculate commission based on offer settings
  const offer = affiliateLink.offer
  let commissionValue: number
  
  if (offer.commissionType === 'PERCENTAGE') {
    commissionValue = (orderValue * Number(offer.commissionRate)) / 100
  } else {
    commissionValue = Number(offer.fixedCommission || 0)
  }
  
  // Create conversion record
  const conversion = await prisma.conversion.create({
    data: {
      affiliateLinkId: affiliateLink.id,
      orderId,
      orderValue,
      commissionValue,
      currency,
      conversionData,
    },
  })
  
  // Update conversion count
  await prisma.affiliateLink.update({
    where: { id: affiliateLink.id },
    data: {
      conversionCount: {
        increment: 1,
      },
    },
  })
  
  // Create commission record
  const commission = await prisma.commission.create({
    data: {
      affiliateId: affiliateLink.affiliateId,
      conversionId: conversion.id,
      amount: commissionValue,
      currency,
    },
  })
  
  // Update affiliate earnings
  await prisma.affiliate.update({
    where: { id: affiliateLink.affiliateId },
    data: {
      pendingEarnings: {
        increment: commissionValue,
      },
    },
  })
  
  return { conversion, commission }
}

export function buildAffiliateUrl(linkCode: string, baseUrl?: string): string {
  const domain = baseUrl || process.env.AFFILIATE_LINK_DOMAIN || 'http://localhost:3000/go'
  return `${domain}/${linkCode}`
}
