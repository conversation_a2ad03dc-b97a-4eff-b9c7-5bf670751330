import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export interface CreatePayoutParams {
  amount: number
  currency: string
  destination: string
  description?: string
  metadata?: Record<string, string>
}

export interface CreateConnectedAccountParams {
  email: string
  country: string
  type: 'express' | 'standard'
  businessType?: 'individual' | 'company'
}

export interface PayoutResult {
  id: string
  amount: number
  currency: string
  status: string
  arrivalDate: number
  description?: string
}

export class StripePaymentService {
  
  /**
   * Create a Stripe Connect account for an affiliate
   */
  async createConnectedAccount(params: CreateConnectedAccountParams): Promise<Stripe.Account> {
    try {
      const account = await stripe.accounts.create({
        type: params.type,
        country: params.country,
        email: params.email,
        business_type: params.businessType,
        capabilities: {
          transfers: { requested: true },
        },
      })
      
      return account
    } catch (error) {
      console.error('Error creating Stripe Connect account:', error)
      throw new Error('Failed to create payment account')
    }
  }

  /**
   * Create an account link for onboarding
   */
  async createAccountLink(accountId: string, refreshUrl: string, returnUrl: string): Promise<Stripe.AccountLink> {
    try {
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: 'account_onboarding',
      })
      
      return accountLink
    } catch (error) {
      console.error('Error creating account link:', error)
      throw new Error('Failed to create account onboarding link')
    }
  }

  /**
   * Get account details and verification status
   */
  async getAccountDetails(accountId: string): Promise<Stripe.Account> {
    try {
      const account = await stripe.accounts.retrieve(accountId)
      return account
    } catch (error) {
      console.error('Error retrieving account details:', error)
      throw new Error('Failed to retrieve account details')
    }
  }

  /**
   * Create a transfer to an affiliate's connected account
   */
  async createTransfer(params: CreatePayoutParams): Promise<Stripe.Transfer> {
    try {
      const transfer = await stripe.transfers.create({
        amount: Math.round(params.amount * 100), // Convert to cents
        currency: params.currency,
        destination: params.destination,
        description: params.description,
        metadata: params.metadata,
      })
      
      return transfer
    } catch (error) {
      console.error('Error creating transfer:', error)
      throw new Error('Failed to process payout')
    }
  }

  /**
   * Create a payout to an affiliate's bank account
   */
  async createPayout(params: CreatePayoutParams): Promise<PayoutResult> {
    try {
      const payout = await stripe.payouts.create({
        amount: Math.round(params.amount * 100), // Convert to cents
        currency: params.currency,
        description: params.description,
        metadata: params.metadata,
      }, {
        stripeAccount: params.destination, // Connected account ID
      })
      
      return {
        id: payout.id,
        amount: payout.amount / 100, // Convert back to dollars
        currency: payout.currency,
        status: payout.status,
        arrivalDate: payout.arrival_date,
        description: payout.description || undefined,
      }
    } catch (error) {
      console.error('Error creating payout:', error)
      throw new Error('Failed to process payout')
    }
  }

  /**
   * Get payout details
   */
  async getPayoutDetails(payoutId: string, accountId: string): Promise<Stripe.Payout> {
    try {
      const payout = await stripe.payouts.retrieve(payoutId, {
        stripeAccount: accountId,
      })
      
      return payout
    } catch (error) {
      console.error('Error retrieving payout details:', error)
      throw new Error('Failed to retrieve payout details')
    }
  }

  /**
   * List payouts for an account
   */
  async listPayouts(accountId: string, limit: number = 10): Promise<Stripe.ApiList<Stripe.Payout>> {
    try {
      const payouts = await stripe.payouts.list({
        limit,
      }, {
        stripeAccount: accountId,
      })
      
      return payouts
    } catch (error) {
      console.error('Error listing payouts:', error)
      throw new Error('Failed to retrieve payouts')
    }
  }

  /**
   * Get account balance
   */
  async getAccountBalance(accountId: string): Promise<Stripe.Balance> {
    try {
      const balance = await stripe.balance.retrieve({
        stripeAccount: accountId,
      })
      
      return balance
    } catch (error) {
      console.error('Error retrieving account balance:', error)
      throw new Error('Failed to retrieve account balance')
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(payload: string, signature: string): Promise<Stripe.Event> {
    try {
      const event = stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
      
      return event
    } catch (error) {
      console.error('Error verifying webhook signature:', error)
      throw new Error('Invalid webhook signature')
    }
  }

  /**
   * Process commission payment batch
   */
  async processCommissionBatch(commissions: Array<{
    id: string
    affiliateId: string
    amount: number
    currency: string
    stripeAccountId: string
  }>): Promise<Array<{ commissionId: string; success: boolean; error?: string; payoutId?: string }>> {
    const results = []
    
    for (const commission of commissions) {
      try {
        const payout = await this.createPayout({
          amount: commission.amount,
          currency: commission.currency,
          destination: commission.stripeAccountId,
          description: `Commission payment for affiliate ${commission.affiliateId}`,
          metadata: {
            commission_id: commission.id,
            affiliate_id: commission.affiliateId,
          },
        })
        
        results.push({
          commissionId: commission.id,
          success: true,
          payoutId: payout.id,
        })
      } catch (error) {
        results.push({
          commissionId: commission.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }
    
    return results
  }
}

export const stripeService = new StripePaymentService()
export default stripe
