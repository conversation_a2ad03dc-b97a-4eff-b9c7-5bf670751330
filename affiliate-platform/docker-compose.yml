version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: affiliate-postgres
    environment:
      POSTGRES_DB: affiliate_platform
      POSTGRES_USER: affiliate_user
      POSTGRES_PASSWORD: affiliate_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U affiliate_user -d affiliate_platform"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: affiliate-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: affiliate-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=************************************************************/affiliate_platform
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
      - STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
      - APP_URL=http://localhost:3000
      - AFFILIATE_LINK_DOMAIN=http://localhost:3000/go
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./prisma:/app/prisma
    command: >
      sh -c "
        npx prisma migrate deploy &&
        npx prisma generate &&
        node server.js
      "

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: affiliate-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:
