# AffiliateHub - Complete Affiliate Marketing Platform

A comprehensive affiliate marketing platform built with Next.js, TypeScript, Prisma, and PostgreSQL. This platform enables brands to create offers and manage affiliate programs while allowing affiliates to browse offers, generate tracking links, and earn commissions.

## 🚀 Features

### For Brands
- **Offer Management**: Create and manage discount offers, cashback deals, and custom promotions
- **Affiliate Network**: Manage relationships with affiliates and track their performance
- **Real-time Analytics**: Monitor clicks, conversions, and commission payouts
- **Commission Control**: Set percentage or fixed commission rates per offer
- **Integration Ready**: Built-in webhook system for e-commerce platform integration

### For Affiliates
- **Offer Discovery**: Browse active offers from various brands
- **Link Generation**: Generate unique tracking links for any offer
- **Performance Tracking**: Monitor clicks, conversions, and earnings in real-time
- **Commission Management**: Track pending and paid commissions
- **Easy Integration**: Simple link sharing across social media and websites

### Platform Features
- **Multi-role Authentication**: Separate dashboards for brands, affiliates, and admins
- **Fraud Prevention**: IP tracking and click validation
- **E-commerce Integration**: Ready-to-use webhooks for Shopify, WooCommerce, etc.
- **Payment Processing**: Stripe integration for commission payouts
- **Real-time Tracking**: Instant click and conversion tracking
- **Responsive Design**: Mobile-friendly interface built with Tailwind CSS

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **Backend**: Next.js API Routes with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Custom JWT-based authentication
- **Payments**: Stripe for commission processing
- **Deployment**: Vercel-ready with environment configuration

### Database Schema
The platform uses a comprehensive database schema with the following core entities:
- **Users**: Multi-role user management (Brand, Affiliate, Admin)
- **Brands**: Company profiles and brand management
- **Affiliates**: Affiliate profiles and earnings tracking
- **Offers**: Promotional offers with commission structures
- **AffiliateLinks**: Unique tracking links with analytics
- **ClickEvents**: Detailed click tracking and analytics
- **Conversions**: Purchase tracking and commission calculation
- **Commissions**: Earnings management and payout tracking

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Stripe account (for payments)

### Installation

1. **Clone and Install Dependencies**
```bash
cd affiliate-platform
npm install
```

2. **Database Setup**
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations (when ready)
npx prisma migrate dev --name init
```

3. **Environment Configuration**
Update the `.env` file with your configuration:
```env
# Database
DATABASE_URL="your-postgresql-connection-string"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
JWT_SECRET="your-jwt-secret"

# Stripe (for payments)
STRIPE_SECRET_KEY="sk_test_your_stripe_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_key"

# Application
APP_URL="http://localhost:3000"
AFFILIATE_LINK_DOMAIN="http://localhost:3000/go"
```

4. **Start Development Server**
```bash
npm run dev
```

The platform will be available at `http://localhost:3000`

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration (Brand/Affiliate)
- `POST /api/auth/login` - User login

### Offers Management
- `GET /api/offers` - List active offers (public)
- `POST /api/offers` - Create new offer (brands only)
- `PUT /api/offers/[id]` - Update offer (brands only)
- `DELETE /api/offers/[id]` - Delete offer (brands only)

### Affiliate Links
- `GET /api/affiliate-links` - Get affiliate's links
- `POST /api/affiliate-links` - Generate new affiliate link
- `GET /go/[linkCode]` - Redirect and track clicks

### Webhooks
- `POST /api/webhooks/conversion` - Record conversions from e-commerce platforms

## 🔗 E-commerce Integration

### Shopify Integration
```javascript
// Example webhook payload for Shopify
{
  "linkCode": "abc123def456",
  "orderId": "shopify_order_123",
  "orderValue": 99.99,
  "currency": "USD",
  "conversionData": {
    "platform": "shopify",
    "customerEmail": "<EMAIL>"
  }
}
```

### WooCommerce Integration
```php
// Example PHP code for WooCommerce
function track_affiliate_conversion($order_id) {
    $order = wc_get_order($order_id);
    $link_code = get_query_var('ref'); // From affiliate link

    if ($link_code) {
        wp_remote_post('https://your-platform.com/api/webhooks/conversion', [
            'body' => json_encode([
                'linkCode' => $link_code,
                'orderId' => $order_id,
                'orderValue' => $order->get_total(),
                'currency' => $order->get_currency()
            ])
        ]);
    }
}
```

## 🎯 Usage Examples

### Brand Workflow
1. Register as a Brand
2. Create promotional offers with commission rates
3. Share offer details with potential affiliates
4. Monitor affiliate performance and approve commissions
5. Process commission payments

### Affiliate Workflow
1. Register as an Affiliate
2. Browse available offers
3. Generate unique tracking links
4. Share links on social media, websites, or email
5. Monitor clicks, conversions, and earnings
6. Receive commission payments

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Access**: Separate permissions for brands and affiliates
- **Click Fraud Prevention**: IP tracking and validation
- **Webhook Verification**: Secure webhook endpoints with signature validation
- **Data Encryption**: Sensitive data encryption in database

## 📊 Analytics & Reporting

The platform provides comprehensive analytics including:
- Click-through rates and conversion tracking
- Commission calculations and payout reports
- Affiliate performance metrics
- Brand campaign effectiveness
- Real-time dashboard updates

## 🚀 Deployment

The platform is ready for deployment on Vercel:

1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy with automatic CI/CD

For production, ensure you:
- Use a production PostgreSQL database
- Configure proper CORS settings
- Set up SSL certificates
- Configure proper webhook endpoints

## 📝 Next Steps

This foundation provides all core functionality for an affiliate marketing platform. Consider adding:
- Advanced analytics dashboards
- Email notification system
- Mobile app for affiliates
- Advanced fraud detection
- Multi-currency support
- Automated payout scheduling

## 🤝 Contributing

This platform is designed to be extensible. Key areas for enhancement:
- Additional e-commerce integrations
- Advanced reporting features
- Mobile applications
- Third-party API integrations
- Enhanced security features

---

Built with ❤️ using Next.js, TypeScript, Prisma, and modern web technologies.
