{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/affiliate/affiliate-platform/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/affiliate/affiliate-platform/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { prisma } from './prisma'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n  role: string\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: JWTPayload): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch {\n    return null\n  }\n}\n\nexport async function createSession(userId: string): Promise<string> {\n  const token = generateToken({ \n    userId, \n    email: '', // Will be filled by the calling function\n    role: '' // Will be filled by the calling function\n  })\n  \n  const expiresAt = new Date()\n  expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n  \n  await prisma.session.create({\n    data: {\n      userId,\n      token,\n      expiresAt,\n    },\n  })\n  \n  return token\n}\n\nexport async function validateSession(token: string) {\n  const session = await prisma.session.findUnique({\n    where: { token },\n    include: { user: true },\n  })\n  \n  if (!session || session.expiresAt < new Date()) {\n    return null\n  }\n  \n  return session\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAQtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAmB;IAC/C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,eAAe,cAAc,MAAc;IAChD,MAAM,QAAQ,cAAc;QAC1B;QACA,OAAO;QACP,MAAM,GAAG,yCAAyC;IACpD;IAEA,MAAM,YAAY,IAAI;IACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,GAAG,kBAAkB;;IAE7D,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1B,MAAM;YACJ;YACA;YACA;QACF;IACF;IAEA,OAAO;AACT;AAEO,eAAe,gBAAgB,KAAa;IACjD,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QAC9C,OAAO;YAAE;QAAM;QACf,SAAS;YAAE,MAAM;QAAK;IACxB;IAEA,IAAI,CAAC,WAAW,QAAQ,SAAS,GAAG,IAAI,QAAQ;QAC9C,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/affiliate/affiliate-platform/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { z } from 'zod'\nimport { prisma } from '@/lib/prisma'\nimport { hashPassword, createSession } from '@/lib/auth'\n\nconst registerSchema = z.object({\n  email: z.string().email(),\n  password: z.string().min(8),\n  name: z.string().min(2),\n  role: z.enum(['BRAND', 'AFFILIATE']),\n  companyName: z.string().optional(), // Required for brands\n  website: z.string().url().optional(),\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const validatedData = registerSchema.parse(body)\n    \n    const { email, password, name, role, companyName, website } = validatedData\n    \n    // Check if user already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email },\n    })\n    \n    if (existingUser) {\n      return NextResponse.json(\n        { error: 'User already exists' },\n        { status: 400 }\n      )\n    }\n    \n    // Hash password\n    const hashedPassword = await hashPassword(password)\n    \n    // Create user and related profile in a transaction\n    const result = await prisma.$transaction(async (tx) => {\n      // Create user\n      const user = await tx.user.create({\n        data: {\n          email,\n          password: hashedPassword,\n          name,\n          role,\n        },\n      })\n      \n      // Create role-specific profile\n      if (role === 'BRAND') {\n        if (!companyName) {\n          throw new Error('Company name is required for brands')\n        }\n        \n        await tx.brand.create({\n          data: {\n            userId: user.id,\n            companyName,\n            website,\n          },\n        })\n      } else if (role === 'AFFILIATE') {\n        await tx.affiliate.create({\n          data: {\n            userId: user.id,\n            displayName: name,\n          },\n        })\n      }\n      \n      return user\n    })\n    \n    // Create session\n    const token = await createSession(result.id)\n    \n    return NextResponse.json({\n      message: 'User registered successfully',\n      user: {\n        id: result.id,\n        email: result.email,\n        name: result.name,\n        role: result.role,\n      },\n      token,\n    })\n    \n  } catch (error) {\n    console.error('Registration error:', error)\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Invalid input data', details: error.errors },\n        { status: 400 }\n      )\n    }\n    \n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;IACvB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAY;IACnC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;AACpC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,eAAe,KAAK,CAAC;QAE3C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;QAE9D,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;QAE1C,mDAAmD;QACnD,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC9C,cAAc;YACd,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAChC,MAAM;oBACJ;oBACA,UAAU;oBACV;oBACA;gBACF;YACF;YAEA,+BAA+B;YAC/B,IAAI,SAAS,SAAS;gBACpB,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;oBACpB,MAAM;wBACJ,QAAQ,KAAK,EAAE;wBACf;wBACA;oBACF;gBACF;YACF,OAAO,IAAI,SAAS,aAAa;gBAC/B,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;oBACxB,MAAM;wBACJ,QAAQ,KAAK,EAAE;wBACf,aAAa;oBACf;gBACF;YACF;YAEA,OAAO;QACT;QAEA,iBAAiB;QACjB,MAAM,QAAQ,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,EAAE;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI,OAAO,EAAE;gBACb,OAAO,OAAO,KAAK;gBACnB,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI;YACnB;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsB,SAAS,MAAM,MAAM;YAAC,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}