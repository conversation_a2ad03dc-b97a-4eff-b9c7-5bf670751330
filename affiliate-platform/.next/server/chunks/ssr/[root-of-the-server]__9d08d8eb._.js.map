{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/affiliate/affiliate-platform/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function LoginPage() {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // Store token and user data\n        localStorage.setItem('token', data.token)\n        localStorage.setItem('user', JSON.stringify(data.user))\n        \n        // Redirect based on user role\n        if (data.user.role === 'BRAND') {\n          router.push('/dashboard/brand')\n        } else if (data.user.role === 'AFFILIATE') {\n          router.push('/dashboard/affiliate')\n        } else {\n          router.push('/dashboard')\n        }\n      } else {\n        setError(data.error || 'Login failed')\n      }\n    } catch (err) {\n      setError('Network error. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">AffiliateHub</h1>\n          <h2 className=\"mt-6 text-2xl font-bold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              href=\"/auth/register\"\n              className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n            >\n              create a new account\n            </Link>\n          </p>\n        </div>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Enter your password\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <a href=\"#\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n                  Forgot your password?\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">New to AffiliateHub?</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-3\">\n              <Link\n                href=\"/auth/register?role=brand\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                Join as Brand\n              </Link>\n              <Link\n                href=\"/auth/register?role=affiliate\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                Join as Affiliate\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,4BAA4B;gBAC5B,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;gBACxC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;gBAErD,8BAA8B;gBAC9B,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,SAAS;oBAC9B,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,aAAa;oBACzC,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;;gCAA6B;gCACrC;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;4BAAY,UAAU;;gCACnC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAmC;;;;;;;;;;;;sDAK5E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAoD;;;;;;;;;;;;;;;;;8CAM9E,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}