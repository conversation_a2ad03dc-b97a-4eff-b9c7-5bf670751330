{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs"], "sourcesContent": ["const config = {\n  plugins: [\"@tailwindcss/postcss\"],\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,SAAS;QAAC;KAAuB;AACnC;uCAEe"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs/transform.ts"], "sourcesContent": ["declare const __turbopack_external_require__: (id: string, thunk: () => any, esm?: boolean) => any;\n\nimport type {Processor} from \"postcss\";\n\n// @ts-ignore\nimport postcss from \"@vercel/turbopack/postcss\";\n// @ts-ignore\nimport importedConfig from \"CONFIG\";\nimport { relative, isAbsolute, sep } from \"path\";\nimport type { Ipc } from \"../ipc/evaluate\";\nimport type { IpcInfoMessage, IpcRequestMessage } from \"./webpack-loaders\";\n\nconst contextDir = process.cwd();\n\nfunction toPath(file: string) {\n  const relPath = relative(contextDir, file);\n  if (isAbsolute(relPath)) {\n    throw new Error(\n      `Cannot depend on path (${file}) outside of root directory (${contextDir})`\n    );\n  }\n  return sep !== \"/\" ? relPath.replaceAll(sep, \"/\") : relPath;\n}\n\nlet processor: Processor | undefined;\n\nexport const init = async (ipc: Ipc<IpcInfoMessage, IpcRequestMessage>) => {\n  let config = importedConfig;\n  if (typeof config === \"function\") {\n    config = await config({ env: \"development\" });\n  }\n  if (typeof config === \"undefined\") {\n    throw new Error(\n      \"PostCSS config is undefined (make sure to export an function or object from config file)\"\n    );\n  }\n  let plugins: any[];\n  if (Array.isArray(config.plugins)) {\n    plugins = config.plugins.map((plugin: [string, any] | string | any) => {\n      if (Array.isArray(plugin)) {\n        return plugin;\n      } else if (typeof plugin === \"string\") {\n        return [plugin, {}];\n      } else {\n        return plugin;\n      }\n    });\n  } else if (typeof config.plugins === \"object\") {\n    plugins = Object.entries(config.plugins).filter(([, options]) => options);\n  } else {\n    plugins = [];\n  }\n  const loadedPlugins = plugins.map((plugin) => {\n    if (Array.isArray(plugin)) {\n      const [arg, options] = plugin;\n      let pluginFactory = arg;\n\n      if (typeof pluginFactory === \"string\") {\n        pluginFactory = require(/* turbopackIgnore: true */ pluginFactory);\n      }\n\n      if (pluginFactory.default) {\n        pluginFactory = pluginFactory.default;\n      }\n\n      return pluginFactory(options);\n    }\n    return plugin;\n  });\n\n  processor = postcss(loadedPlugins);\n};\n\nexport default async function transform(\n  ipc: Ipc<IpcInfoMessage, IpcRequestMessage>,\n  cssContent: string,\n  name: string,\n  sourceMap: boolean\n) {\n  const { css, map, messages } = await processor!.process(cssContent, {\n    from: name,\n    to: name,\n    map: sourceMap ? {\n      inline: false,\n      annotation: false,\n    } : undefined,\n  });\n\n  const assets = [];\n  for (const msg of messages) {\n    switch (msg.type) {\n      case \"asset\":\n        assets.push({\n          file: msg.file,\n          content: msg.content,\n          sourceMap:\n            !sourceMap\n              ? undefined\n              : typeof msg.sourceMap === \"string\"\n              ? msg.sourceMap\n              : JSON.stringify(msg.sourceMap),\n          // There is also an info field, which we currently ignore\n        });\n        break;\n      case \"dependency\":\n      case \"missing-dependency\":\n        ipc.sendInfo({\n          type: \"fileDependency\",\n          path: toPath(msg.file),\n        });\n        break;\n      case \"build-dependency\":\n        ipc.sendInfo({\n          type: \"buildDependency\",\n          path: toPath(msg.file),\n        });\n        break;\n      case \"dir-dependency\":\n        ipc.sendInfo({\n          type: \"dirDependency\",\n          path: toPath(msg.dir),\n          glob: msg.glob,\n        });\n        break;\n      case \"context-dependency\":\n        ipc.sendInfo({\n          type: \"dirDependency\",\n          path: toPath(msg.file),\n          glob: \"**\",\n        });\n        break;\n      default:\n        // TODO: do we need to do anything here?\n        break;\n    }\n  }\n  return {\n    css,\n    map: sourceMap ? JSON.stringify(map) : undefined,\n    assets,\n  };\n}\n"], "names": [], "mappings": ";;;;AAIA,aAAa;AACb;AACA,aAAa;AACb;AACA;;;;AAIA,MAAM,aAAa,QAAQ,GAAG;AAE9B,SAAS,OAAO,IAAY;IAC1B,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IACrC,IAAI,CAAA,GAAA,iGAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QACvB,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAE/E;IACA,OAAO,iGAAA,CAAA,MAAG,KAAK,MAAM,QAAQ,UAAU,CAAC,iGAAA,CAAA,MAAG,EAAE,OAAO;AACtD;AAEA,IAAI;AAEG,MAAM,OAAO,OAAO;IACzB,IAAI,SAAS,+GAAA,CAAA,UAAc;IAC3B,IAAI,OAAO,WAAW,YAAY;QAChC,SAAS,MAAM,OAAO;YAAE,KAAK;QAAc;IAC7C;IACA,IAAI,OAAO,WAAW,aAAa;QACjC,MAAM,IAAI,MACR;IAEJ;IACA,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,GAAG;QACjC,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,OAAO;YACT,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,OAAO;oBAAC;oBAAQ,CAAC;iBAAE;YACrB,OAAO;gBACL,OAAO;YACT;QACF;IACF,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;QAC7C,UAAU,OAAO,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAK;IACnE,OAAO;QACL,UAAU,EAAE;IACd;IACA,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAC;QACjC,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,MAAM,CAAC,KAAK,QAAQ,GAAG;YACvB,IAAI,gBAAgB;YAEpB,IAAI,OAAO,kBAAkB,UAAU;gBACrC,gBAAgB,QAAQ,yBAAyB,GAAG;YACtD;YAEA,IAAI,cAAc,OAAO,EAAE;gBACzB,gBAAgB,cAAc,OAAO;YACvC;YAEA,OAAO,cAAc;QACvB;QACA,OAAO;IACT;IAEA,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;AACtB;AAEe,eAAe,UAC5B,GAA2C,EAC3C,UAAkB,EAClB,IAAY,EACZ,SAAkB;IAElB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,UAAW,OAAO,CAAC,YAAY;QAClE,MAAM;QACN,IAAI;QACJ,KAAK,YAAY;YACf,QAAQ;YACR,YAAY;QACd,IAAI;IACN;IAEA,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,OAAO,SAAU;QAC1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,OAAO,IAAI,CAAC;oBACV,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;oBACpB,WACE,CAAC,YACG,YACA,OAAO,IAAI,SAAS,KAAK,WACzB,IAAI,SAAS,GACb,KAAK,SAAS,CAAC,IAAI,SAAS;gBAEpC;gBACA;YACF,KAAK;YACL,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;gBACvB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;gBACvB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,GAAG;oBACpB,MAAM,IAAI,IAAI;gBAChB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;oBACrB,MAAM;gBACR;gBACA;YACF;gBAEE;QACJ;IACF;IACA,OAAO;QACL;QACA,KAAK,YAAY,KAAK,SAAS,CAAC,OAAO;QACvC;IACF;AACF"}}]}