# AffiliateHub API Documentation

This document provides comprehensive API documentation for the AffiliateHub affiliate marketing platform.

## Base URL
```
https://your-platform.com/api
```

## Authentication

Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "error": null
}
```

Error responses:
```json
{
  "success": false,
  "data": null,
  "message": null,
  "error": "Error message"
}
```

## Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user (brand or affiliate).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "<PERSON>",
  "role": "BRAND", // or "AFFILIATE"
  "companyName": "Acme Corp", // Required for brands
  "website": "https://example.com" // Optional
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "<PERSON> Doe",
    "role": "BRAND"
  },
  "token": "jwt_token_here"
}
```

### Login User
**POST** `/auth/login`

Authenticate a user and receive a JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "BRAND",
    "brand": {
      "id": "brand_id",
      "companyName": "Acme Corp",
      "status": "APPROVED"
    }
  },
  "token": "jwt_token_here"
}
```

## Offers Management

### List Offers
**GET** `/offers`

Get a paginated list of active offers.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `category` (string): Filter by category
- `search` (string): Search in title/description
- `status` (string): Filter by status (default: "ACTIVE")

**Response:**
```json
{
  "offers": [
    {
      "id": "offer_id",
      "title": "20% Off Summer Sale",
      "description": "Get 20% off all summer items",
      "offerType": "DISCOUNT",
      "discountValue": 20,
      "discountType": "PERCENTAGE",
      "commissionRate": 5.0,
      "commissionType": "PERCENTAGE",
      "category": "Fashion",
      "imageUrl": "https://example.com/image.jpg",
      "startDate": "2024-06-01T00:00:00Z",
      "endDate": "2024-08-31T23:59:59Z",
      "status": "ACTIVE",
      "brand": {
        "id": "brand_id",
        "companyName": "Fashion Store",
        "logo": "https://example.com/logo.jpg"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Offer
**POST** `/offers`

Create a new offer (brands only).

**Headers:**
```
Authorization: Bearer <brand-jwt-token>
```

**Request Body:**
```json
{
  "title": "Summer Sale 2024",
  "description": "Amazing discounts on summer collection",
  "terms": "Valid until August 31, 2024",
  "offerType": "DISCOUNT",
  "discountValue": 25,
  "discountType": "PERCENTAGE",
  "commissionRate": 8.0,
  "commissionType": "PERCENTAGE",
  "category": "Fashion",
  "tags": ["summer", "sale", "discount"],
  "imageUrl": "https://example.com/offer-image.jpg",
  "startDate": "2024-06-01T00:00:00Z",
  "endDate": "2024-08-31T23:59:59Z",
  "maxCommissions": 1000
}
```

**Response:**
```json
{
  "message": "Offer created successfully",
  "offer": {
    "id": "new_offer_id",
    "title": "Summer Sale 2024",
    // ... other offer fields
    "status": "DRAFT"
  }
}
```

## Affiliate Links Management

### Get Affiliate Links
**GET** `/affiliate-links`

Get affiliate's generated links.

**Headers:**
```
Authorization: Bearer <affiliate-jwt-token>
```

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**
```json
{
  "links": [
    {
      "id": "link_id",
      "linkCode": "abc123def456",
      "originalUrl": "https://brand.com/product",
      "affiliateUrl": "https://your-platform.com/go/abc123def456",
      "clicks": 150,
      "conversionCount": 12,
      "isActive": true,
      "createdAt": "2024-06-01T10:00:00Z",
      "offer": {
        "id": "offer_id",
        "title": "Summer Sale",
        "commissionRate": 5.0,
        "brand": {
          "companyName": "Fashion Store"
        }
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "pages": 1
  }
}
```

### Generate Affiliate Link
**POST** `/affiliate-links`

Generate a new affiliate tracking link.

**Headers:**
```
Authorization: Bearer <affiliate-jwt-token>
```

**Request Body:**
```json
{
  "offerId": "offer_id",
  "originalUrl": "https://brand.com/product-page"
}
```

**Response:**
```json
{
  "message": "Affiliate link generated successfully",
  "link": {
    "id": "link_id",
    "linkCode": "abc123def456",
    "originalUrl": "https://brand.com/product-page",
    "affiliateUrl": "https://your-platform.com/go/abc123def456",
    "clicks": 0,
    "conversionCount": 0,
    "isActive": true,
    "offer": {
      "title": "Summer Sale",
      "commissionRate": 5.0
    }
  }
}
```

## Tracking & Analytics

### Track Click
**GET** `/go/{linkCode}`

Redirect and track affiliate link clicks.

**Path Parameters:**
- `linkCode` (string): Unique link identifier

**Response:**
- HTTP 302 redirect to original URL
- Click event recorded in database

### Record Conversion
**POST** `/webhooks/conversion`

Record a conversion from e-commerce platforms.

**Headers:**
```
Content-Type: application/json
X-Webhook-Secret: <your-webhook-secret>
```

**Request Body:**
```json
{
  "linkCode": "abc123def456",
  "orderId": "order_12345",
  "orderValue": 99.99,
  "currency": "USD",
  "conversionData": {
    "platform": "shopify",
    "customerEmail": "<EMAIL>",
    "customerName": "Jane Doe",
    "products": [
      {
        "id": "product_1",
        "name": "Summer Dress",
        "quantity": 1,
        "price": 99.99
      }
    ]
  }
}
```

**Response:**
```json
{
  "message": "Conversion recorded successfully",
  "conversion": {
    "id": "conversion_id",
    "orderId": "order_12345",
    "orderValue": 99.99,
    "commissionValue": 5.00,
    "status": "PENDING"
  },
  "commission": {
    "id": "commission_id",
    "amount": 5.00,
    "status": "PENDING"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 500 | Internal Server Error |

## Rate Limiting

API requests are limited to:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated requests
- 10 requests per minute for webhook endpoints

## Webhooks

### Webhook Security

All webhooks include a signature in the `X-Webhook-Secret` header for verification.

### Supported Events

1. **Conversion Created** - When a new conversion is recorded
2. **Commission Approved** - When a commission is approved for payment
3. **Affiliate Link Created** - When a new affiliate link is generated

## SDKs and Libraries

### JavaScript/Node.js
```javascript
const AffiliateHub = require('@affiliatehub/sdk');

const client = new AffiliateHub({
  apiUrl: 'https://your-platform.com/api',
  apiKey: 'your-api-key'
});

// Generate affiliate link
const link = await client.affiliateLinks.create({
  offerId: 'offer_123',
  originalUrl: 'https://brand.com/product'
});
```

### PHP
```php
use AffiliateHub\Client;

$client = new Client([
    'api_url' => 'https://your-platform.com/api',
    'api_key' => 'your-api-key'
]);

// Record conversion
$conversion = $client->conversions->create([
    'linkCode' => 'abc123',
    'orderId' => 'order_123',
    'orderValue' => 99.99
]);
```

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.affiliatehub.com
- Status Page: https://status.affiliatehub.com
