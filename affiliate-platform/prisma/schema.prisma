// Affiliate Marketing Platform Database Schema
// This schema defines all the core entities for the affiliate marketing platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model - handles authentication for all user types
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      UserRole @default(AFFILIATE)
  verified  <PERSON><PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  brand     Brand?
  affiliate Affiliate?
  sessions  Session[]

  @@map("users")
}

// Session model for authentication
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Brand model - companies that create offers
model Brand {
  id          String      @id @default(cuid())
  userId      String      @unique
  companyName String
  website     String?
  description String?
  logo        String?
  status      BrandStatus @default(PENDING)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  offers Offer[]

  @@map("brands")
}

// Affiliate model - users who promote offers
model Affiliate {
  id              String           @id @default(cuid())
  userId          String           @unique
  displayName     String?
  bio             String?
  website         String?
  socialMedia     Json?            // Store social media links as JSON
  paymentInfo     Json?            // Store payment details as JSON
  status          AffiliateStatus  @default(ACTIVE)
  totalEarnings   Decimal          @default(0)
  pendingEarnings Decimal          @default(0)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  affiliateLinks  AffiliateLink[]
  commissions     Commission[]

  @@map("affiliates")
}

// Offer model - deals/promotions created by brands
model Offer {
  id              String      @id @default(cuid())
  brandId         String
  title           String
  description     String
  terms           String?
  offerType       OfferType
  discountValue   Decimal?
  discountType    DiscountType?
  commissionRate  Decimal     // Percentage
  commissionType  CommissionType @default(PERCENTAGE)
  fixedCommission Decimal?
  category        String?
  tags            String? // Comma-separated tags
  imageUrl        String?
  startDate       DateTime?
  endDate         DateTime?
  status          OfferStatus @default(DRAFT)
  maxCommissions  Int?        // Optional limit on total commissions
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  brand          Brand           @relation(fields: [brandId], references: [id], onDelete: Cascade)
  affiliateLinks AffiliateLink[]

  @@map("offers")
}

// AffiliateLink model - unique tracking links generated by affiliates
model AffiliateLink {
  id          String    @id @default(cuid())
  affiliateId String
  offerId     String
  linkCode    String    @unique // Unique identifier for the link
  originalUrl String    // The brand's original URL
  clicks          Int       @default(0)
  conversionCount Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  affiliate   Affiliate   @relation(fields: [affiliateId], references: [id], onDelete: Cascade)
  offer       Offer       @relation(fields: [offerId], references: [id], onDelete: Cascade)
  clickEvents ClickEvent[]
  conversions Conversion[]

  @@unique([affiliateId, offerId])
  @@map("affiliate_links")
}

// ClickEvent model - tracks when affiliate links are clicked
model ClickEvent {
  id              String    @id @default(cuid())
  affiliateLinkId String
  ipAddress       String?
  userAgent       String?
  referrer        String?
  country         String?
  city            String?
  device          String?
  browser         String?
  timestamp       DateTime  @default(now())

  // Relations
  affiliateLink AffiliateLink @relation(fields: [affiliateLinkId], references: [id], onDelete: Cascade)

  @@map("click_events")
}

// Conversion model - tracks successful purchases/actions
model Conversion {
  id              String          @id @default(cuid())
  affiliateLinkId String
  orderId         String?         // External order ID from e-commerce platform
  orderValue      Decimal
  commissionValue Decimal
  currency        String          @default("USD")
  status          ConversionStatus @default(PENDING)
  conversionData  Json?           // Additional conversion data
  timestamp       DateTime        @default(now())
  verifiedAt      DateTime?

  // Relations
  affiliateLink AffiliateLink @relation(fields: [affiliateLinkId], references: [id], onDelete: Cascade)
  commission    Commission?

  @@map("conversions")
}

// Commission model - tracks earnings for affiliates
model Commission {
  id           String           @id @default(cuid())
  affiliateId  String
  conversionId String           @unique
  amount       Decimal
  currency     String           @default("USD")
  status       CommissionStatus @default(PENDING)
  paidAt       DateTime?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relations
  affiliate  Affiliate  @relation(fields: [affiliateId], references: [id], onDelete: Cascade)
  conversion Conversion @relation(fields: [conversionId], references: [id], onDelete: Cascade)

  @@map("commissions")
}

// Integration model - tracks e-commerce platform integrations
model Integration {
  id            String            @id @default(cuid())
  brandId       String?
  platform      IntegrationType
  credentials   Json              // Encrypted API keys/credentials
  webhookUrl    String?
  isActive      Boolean           @default(true)
  lastSyncAt    DateTime?
  configuration Json?             // Platform-specific configuration
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  @@map("integrations")
}

// Enums
enum UserRole {
  ADMIN
  BRAND
  AFFILIATE
}

enum BrandStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum AffiliateStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum OfferType {
  DISCOUNT
  CASHBACK
  FREE_SHIPPING
  BOGO
  CUSTOM
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum CommissionType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum OfferStatus {
  DRAFT
  ACTIVE
  PAUSED
  EXPIRED
  CANCELLED
}

enum ConversionStatus {
  PENDING
  CONFIRMED
  REJECTED
  REFUNDED
}

enum CommissionStatus {
  PENDING
  APPROVED
  PAID
  CANCELLED
}

enum IntegrationType {
  SHOPIFY
  WOOCOMMERCE
  MAGENTO
  BIGCOMMERCE
  CUSTOM_API
}
