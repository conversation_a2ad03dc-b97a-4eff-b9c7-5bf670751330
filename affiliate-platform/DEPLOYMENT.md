# AffiliateHub Deployment Guide

This guide covers various deployment options for the AffiliateHub affiliate marketing platform.

## 🚀 Quick Start (Local Development)

### Prerequisites
- Node.js 18+
- PostgreSQL 15+
- Redis (optional, for caching)

### Local Setup
```bash
# Clone the repository
git clone <your-repo-url>
cd affiliate-platform

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up database
npx prisma migrate dev --name init
npx prisma generate

# Start development server
npm run dev
```

## 🐳 Docker Deployment

### Development with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Production with Docker Compose
```bash
# Use production profile
docker-compose --profile production up -d

# This includes Nginx reverse proxy with SSL
```

## ☁️ Cloud Deployment Options

### 1. Vercel (Recommended for Quick Deploy)

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel
   ```

2. **Environment Variables**
   Set these in Vercel dashboard:
   ```
   DATABASE_URL=postgresql://...
   JWT_SECRET=your-secret-key
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   APP_URL=https://your-domain.vercel.app
   ```

3. **Database Setup**
   - Use Vercel Postgres or external PostgreSQL
   - Run migrations: `npx prisma migrate deploy`

### 2. AWS Deployment

#### Option A: AWS App Runner
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com

docker build -t affiliate-platform .
docker tag affiliate-platform:latest <account>.dkr.ecr.us-east-1.amazonaws.com/affiliate-platform:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/affiliate-platform:latest

# Create App Runner service via AWS Console
```

#### Option B: ECS with Fargate
```bash
# Use the provided ECS task definition
aws ecs create-service --cli-input-json file://aws/ecs-service.json
```

### 3. Google Cloud Platform

#### Cloud Run Deployment
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/affiliate-platform
gcloud run deploy --image gcr.io/PROJECT-ID/affiliate-platform --platform managed
```

### 4. Kubernetes Deployment

#### Prerequisites
- Kubernetes cluster (GKE, EKS, AKS, or self-managed)
- kubectl configured
- Ingress controller (nginx-ingress)
- Cert-manager for SSL certificates

#### Deploy to Kubernetes
```bash
# Create namespace
kubectl create namespace affiliate-platform

# Apply configurations
kubectl apply -f k8s/ -n affiliate-platform

# Check deployment status
kubectl get pods -n affiliate-platform
kubectl get services -n affiliate-platform
kubectl get ingress -n affiliate-platform
```

#### Update Secrets
```bash
# Update database URL
kubectl create secret generic affiliate-secrets \
  --from-literal=database-url="********************************/db" \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=stripe-secret-key="sk_live_..." \
  --from-literal=stripe-webhook-secret="whsec_..." \
  -n affiliate-platform
```

## 🗄️ Database Setup

### PostgreSQL Configuration

#### Production Database Setup
```sql
-- Create database and user
CREATE DATABASE affiliate_platform;
CREATE USER affiliate_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE affiliate_platform TO affiliate_user;

-- Connect to the database
\c affiliate_platform

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO affiliate_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO affiliate_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO affiliate_user;
```

#### Run Migrations
```bash
# Production migration
DATABASE_URL="********************************/db" npx prisma migrate deploy

# Generate client
npx prisma generate
```

### Database Backup Strategy
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backups/affiliate_platform_$DATE.sql

# Retention: keep last 30 days
find backups/ -name "*.sql" -mtime +30 -delete
```

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Using Let's Encrypt with Certbot
sudo certbot --nginx -d your-domain.com

# Or use cert-manager in Kubernetes
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
```

### Environment Variables (Production)
```bash
# Required environment variables
NODE_ENV=production
DATABASE_URL=********************************/db
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
STRIPE_SECRET_KEY=sk_live_your_live_stripe_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
APP_URL=https://your-domain.com
AFFILIATE_LINK_DOMAIN=https://your-domain.com/go

# Optional
REDIS_URL=redis://localhost:6379
SENTRY_DSN=https://your-sentry-dsn
```

## 📊 Monitoring & Logging

### Health Checks
The application provides health check endpoints:
- `GET /health` - Basic health check
- `GET /api/health` - Detailed health with database connectivity

### Logging
```bash
# View application logs
docker-compose logs -f app

# Kubernetes logs
kubectl logs -f deployment/affiliate-platform -n affiliate-platform
```

### Monitoring Setup
```bash
# Prometheus metrics endpoint
GET /api/metrics

# Set up monitoring stack
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install monitoring prometheus-community/kube-prometheus-stack
```

## 🔄 CI/CD Pipeline

### GitHub Actions
The repository includes a complete CI/CD pipeline:
- Automated testing
- Security scanning
- Docker image building
- Deployment to staging/production

### Manual Deployment
```bash
# Build production image
docker build -t affiliate-platform:latest .

# Tag for registry
docker tag affiliate-platform:latest your-registry/affiliate-platform:v1.0.0

# Push to registry
docker push your-registry/affiliate-platform:v1.0.0

# Deploy to production
kubectl set image deployment/affiliate-platform app=your-registry/affiliate-platform:v1.0.0 -n affiliate-platform
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Test database connectivity
   npx prisma db pull
   
   # Check connection string format
   postgresql://username:password@hostname:port/database
   ```

2. **Migration Failures**
   ```bash
   # Reset database (development only)
   npx prisma migrate reset
   
   # Force migration
   npx prisma db push --force-reset
   ```

3. **Build Failures**
   ```bash
   # Clear Next.js cache
   rm -rf .next
   
   # Regenerate Prisma client
   npx prisma generate
   ```

4. **Performance Issues**
   ```bash
   # Check resource usage
   kubectl top pods -n affiliate-platform
   
   # Scale deployment
   kubectl scale deployment affiliate-platform --replicas=5 -n affiliate-platform
   ```

### Logs Analysis
```bash
# Application errors
kubectl logs -f deployment/affiliate-platform -n affiliate-platform | grep ERROR

# Database queries (development)
DEBUG=prisma:query npm run dev
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use Kubernetes HPA for automatic scaling
- Configure load balancing
- Implement Redis for session storage

### Database Scaling
- Set up read replicas
- Implement connection pooling
- Consider database sharding for high traffic

### CDN Setup
```bash
# CloudFlare setup
# 1. Add domain to CloudFlare
# 2. Configure DNS records
# 3. Enable caching rules for static assets
```

## 🔐 Backup & Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
pg_dump $DATABASE_URL | gzip > backup_$(date +%Y%m%d).sql.gz
aws s3 cp backup_$(date +%Y%m%d).sql.gz s3://your-backup-bucket/
```

### Application Backup
```bash
# Backup uploaded files and configurations
tar -czf app_backup_$(date +%Y%m%d).tar.gz uploads/ config/
```

This deployment guide covers all major deployment scenarios. Choose the option that best fits your infrastructure and requirements.
