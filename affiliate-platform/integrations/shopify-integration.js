/**
 * AffiliateHub Shopify Integration
 * 
 * This script provides integration between Shopify and AffiliateHub
 * for affiliate link tracking and conversion reporting.
 */

class AffiliateHubShopify {
    constructor(config) {
        this.apiUrl = config.apiUrl || 'https://your-platform.com';
        this.webhookSecret = config.webhookSecret;
        this.shopifyDomain = config.shopifyDomain;
        this.accessToken = config.accessToken;
        
        this.init();
    }
    
    init() {
        // Set up tracking on page load
        this.setupTracking();
        
        // Set up webhook endpoints if running on server
        if (typeof window === 'undefined') {
            this.setupWebhooks();
        }
    }
    
    setupTracking() {
        // Client-side tracking for affiliate links
        if (typeof window !== 'undefined') {
            this.trackAffiliateVisit();
            this.setupConversionTracking();
        }
    }
    
    trackAffiliateVisit() {
        // Check for affiliate reference in URL
        const urlParams = new URLSearchParams(window.location.search);
        const ref = urlParams.get('ref') || urlParams.get('affiliate');
        
        if (ref) {
            // Store affiliate reference in localStorage and cookie
            localStorage.setItem('affiliate_ref', ref);
            this.setCookie('affiliate_ref', ref, 30); // 30 days
            
            // Track the click
            this.sendClickEvent(ref);
        }
    }
    
    sendClickEvent(linkCode) {
        const clickData = {
            linkCode: linkCode,
            pageUrl: window.location.href,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };
        
        // Send to AffiliateHub tracking endpoint
        fetch(`${this.apiUrl}/api/affiliate-links/track-click`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(clickData)
        }).catch(error => {
            console.error('AffiliateHub click tracking error:', error);
        });
    }
    
    setupConversionTracking() {
        // Track conversions on thank you page
        if (window.location.pathname.includes('/thank_you') || 
            window.location.pathname.includes('/orders/')) {
            
            const affiliateRef = localStorage.getItem('affiliate_ref') || 
                                this.getCookie('affiliate_ref');
            
            if (affiliateRef && window.Shopify && window.Shopify.checkout) {
                this.trackConversion(affiliateRef, window.Shopify.checkout);
            }
        }
    }
    
    trackConversion(linkCode, checkout) {
        const conversionData = {
            linkCode: linkCode,
            orderId: checkout.order_id || checkout.id,
            orderValue: parseFloat(checkout.total_price),
            currency: checkout.currency,
            conversionData: {
                platform: 'shopify',
                customerEmail: checkout.email,
                customerName: `${checkout.billing_address?.first_name || ''} ${checkout.billing_address?.last_name || ''}`.trim(),
                orderDate: new Date().toISOString(),
                products: this.formatProducts(checkout.line_items),
                shippingAddress: checkout.shipping_address,
                billingAddress: checkout.billing_address
            }
        };
        
        // Send conversion to AffiliateHub
        fetch(`${this.apiUrl}/api/webhooks/conversion`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Webhook-Secret': this.webhookSecret
            },
            body: JSON.stringify(conversionData)
        }).then(response => {
            if (response.ok) {
                // Clear affiliate reference after successful tracking
                localStorage.removeItem('affiliate_ref');
                this.setCookie('affiliate_ref', '', -1);
            }
        }).catch(error => {
            console.error('AffiliateHub conversion tracking error:', error);
        });
    }
    
    formatProducts(lineItems) {
        if (!lineItems) return [];
        
        return lineItems.map(item => ({
            id: item.product_id,
            variantId: item.variant_id,
            name: item.title,
            quantity: item.quantity,
            price: parseFloat(item.price),
            sku: item.sku
        }));
    }
    
    setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }
    
    getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
    
    // Server-side webhook setup (Node.js/Express example)
    setupWebhooks() {
        const express = require('express');
        const crypto = require('crypto');
        
        const app = express();
        app.use(express.json());
        
        // Shopify webhook verification middleware
        const verifyShopifyWebhook = (req, res, next) => {
            const hmac = req.get('X-Shopify-Hmac-Sha256');
            const body = JSON.stringify(req.body);
            const hash = crypto
                .createHmac('sha256', this.webhookSecret)
                .update(body, 'utf8')
                .digest('base64');
            
            if (hash === hmac) {
                next();
            } else {
                res.status(401).send('Unauthorized');
            }
        };
        
        // Handle order creation webhook
        app.post('/webhooks/shopify/orders/create', verifyShopifyWebhook, (req, res) => {
            const order = req.body;
            this.handleOrderCreated(order);
            res.status(200).send('OK');
        });
        
        // Handle order payment webhook
        app.post('/webhooks/shopify/orders/paid', verifyShopifyWebhook, (req, res) => {
            const order = req.body;
            this.handleOrderPaid(order);
            res.status(200).send('OK');
        });
    }
    
    async handleOrderCreated(order) {
        // Check if order has affiliate reference
        const affiliateRef = this.extractAffiliateRef(order);
        
        if (affiliateRef) {
            const conversionData = {
                linkCode: affiliateRef,
                orderId: order.id.toString(),
                orderValue: parseFloat(order.total_price),
                currency: order.currency,
                conversionData: {
                    platform: 'shopify',
                    customerEmail: order.email,
                    customerName: `${order.customer?.first_name || ''} ${order.customer?.last_name || ''}`.trim(),
                    orderDate: order.created_at,
                    products: order.line_items.map(item => ({
                        id: item.product_id,
                        variantId: item.variant_id,
                        name: item.title,
                        quantity: item.quantity,
                        price: parseFloat(item.price)
                    })),
                    orderStatus: order.financial_status
                }
            };
            
            // Send to AffiliateHub
            await this.sendConversionWebhook(conversionData);
        }
    }
    
    extractAffiliateRef(order) {
        // Check order attributes for affiliate reference
        if (order.note_attributes) {
            const refAttr = order.note_attributes.find(attr => 
                attr.name === 'affiliate_ref' || attr.name === 'ref'
            );
            if (refAttr) return refAttr.value;
        }
        
        // Check landing site URL for reference
        if (order.landing_site) {
            const url = new URL(order.landing_site);
            return url.searchParams.get('ref') || url.searchParams.get('affiliate');
        }
        
        return null;
    }
    
    async sendConversionWebhook(data) {
        try {
            const response = await fetch(`${this.apiUrl}/api/webhooks/conversion`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Webhook-Secret': this.webhookSecret
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            console.log('AffiliateHub conversion tracked successfully');
        } catch (error) {
            console.error('AffiliateHub webhook error:', error);
        }
    }
}

// Usage examples:

// Client-side initialization (in theme.liquid)
/*
<script>
document.addEventListener('DOMContentLoaded', function() {
    const affiliateHub = new AffiliateHubShopify({
        apiUrl: 'https://your-platform.com',
        webhookSecret: 'your-webhook-secret'
    });
});
</script>
*/

// Server-side initialization (Node.js)
/*
const affiliateHub = new AffiliateHubShopify({
    apiUrl: 'https://your-platform.com',
    webhookSecret: 'your-shopify-webhook-secret',
    shopifyDomain: 'your-shop.myshopify.com',
    accessToken: 'your-shopify-access-token'
});
*/

module.exports = AffiliateHubShopify;
