<?php
/**
 * Plugin Name: AffiliateHub WooCommerce Integration
 * Description: Integrates WooCommerce with AffiliateHub affiliate marketing platform
 * Version: 1.0.0
 * Author: AffiliateHub
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AffiliateHubWooCommerce {
    
    private $api_url;
    private $webhook_secret;
    
    public function __construct() {
        $this->api_url = get_option('affiliatehub_api_url', 'https://your-platform.com');
        $this->webhook_secret = get_option('affiliatehub_webhook_secret', '');
        
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('woocommerce_thankyou', array($this, 'track_conversion'), 10, 1);
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function init() {
        // Store affiliate link code in session when user visits with ref parameter
        if (isset($_GET['ref']) && !empty($_GET['ref'])) {
            if (!session_id()) {
                session_start();
            }
            $_SESSION['affiliate_ref'] = sanitize_text_field($_GET['ref']);
            
            // Also store in cookie for 30 days
            setcookie('affiliate_ref', sanitize_text_field($_GET['ref']), time() + (30 * 24 * 60 * 60), '/');
        }
    }
    
    public function enqueue_scripts() {
        // Add tracking script for click events
        wp_enqueue_script('affiliatehub-tracking', plugin_dir_url(__FILE__) . 'tracking.js', array('jquery'), '1.0.0', true);
        wp_localize_script('affiliatehub-tracking', 'affiliatehub_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('affiliatehub_nonce')
        ));
    }
    
    public function track_conversion($order_id) {
        if (!$order_id) return;
        
        $order = wc_get_order($order_id);
        if (!$order) return;
        
        // Get affiliate reference from session or cookie
        $affiliate_ref = null;
        if (!session_id()) {
            session_start();
        }
        
        if (isset($_SESSION['affiliate_ref'])) {
            $affiliate_ref = $_SESSION['affiliate_ref'];
        } elseif (isset($_COOKIE['affiliate_ref'])) {
            $affiliate_ref = $_COOKIE['affiliate_ref'];
        }
        
        if (!$affiliate_ref) return;
        
        // Prepare conversion data
        $conversion_data = array(
            'linkCode' => $affiliate_ref,
            'orderId' => $order->get_id(),
            'orderValue' => floatval($order->get_total()),
            'currency' => $order->get_currency(),
            'conversionData' => array(
                'platform' => 'woocommerce',
                'customerEmail' => $order->get_billing_email(),
                'customerName' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
                'orderDate' => $order->get_date_created()->format('Y-m-d H:i:s'),
                'products' => $this->get_order_products($order)
            )
        );
        
        // Send to AffiliateHub
        $this->send_conversion_webhook($conversion_data);
        
        // Clear the affiliate reference
        unset($_SESSION['affiliate_ref']);
        setcookie('affiliate_ref', '', time() - 3600, '/');
    }
    
    private function get_order_products($order) {
        $products = array();
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            $products[] = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'quantity' => $item->get_quantity(),
                'price' => floatval($item->get_total())
            );
        }
        return $products;
    }
    
    private function send_conversion_webhook($data) {
        $webhook_url = $this->api_url . '/api/webhooks/conversion';
        
        $args = array(
            'body' => json_encode($data),
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-Webhook-Secret' => $this->webhook_secret
            ),
            'timeout' => 30
        );
        
        $response = wp_remote_post($webhook_url, $args);
        
        if (is_wp_error($response)) {
            error_log('AffiliateHub webhook error: ' . $response->get_error_message());
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                error_log('AffiliateHub webhook failed with status: ' . $response_code);
            }
        }
    }
    
    public function admin_menu() {
        add_options_page(
            'AffiliateHub Settings',
            'AffiliateHub',
            'manage_options',
            'affiliatehub-settings',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        if (isset($_POST['submit'])) {
            update_option('affiliatehub_api_url', sanitize_url($_POST['api_url']));
            update_option('affiliatehub_webhook_secret', sanitize_text_field($_POST['webhook_secret']));
            echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
        }
        
        $api_url = get_option('affiliatehub_api_url', 'https://your-platform.com');
        $webhook_secret = get_option('affiliatehub_webhook_secret', '');
        ?>
        <div class="wrap">
            <h1>AffiliateHub Settings</h1>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="api_url" value="<?php echo esc_attr($api_url); ?>" class="regular-text" />
                            <p class="description">Your AffiliateHub platform URL</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Webhook Secret</th>
                        <td>
                            <input type="text" name="webhook_secret" value="<?php echo esc_attr($webhook_secret); ?>" class="regular-text" />
                            <p class="description">Secret key for webhook authentication</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
            
            <h2>Usage Instructions</h2>
            <p>1. Configure your AffiliateHub platform URL and webhook secret above</p>
            <p>2. Affiliate links should include the <code>?ref=LINKCODE</code> parameter</p>
            <p>3. Example: <code>https://yourstore.com/product/item?ref=abc123def456</code></p>
            <p>4. Conversions will be automatically tracked when customers complete purchases</p>
        </div>
        <?php
    }
}

// Initialize the plugin
new AffiliateHubWooCommerce();

// JavaScript tracking file content for tracking.js
?>
<script>
// This would be in a separate tracking.js file
jQuery(document).ready(function($) {
    // Track page views with affiliate reference
    if (window.location.search.includes('ref=')) {
        var urlParams = new URLSearchParams(window.location.search);
        var ref = urlParams.get('ref');
        
        if (ref) {
            // Send click tracking to AffiliateHub
            $.ajax({
                url: affiliatehub_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'track_affiliate_click',
                    ref: ref,
                    nonce: affiliatehub_ajax.nonce,
                    page_url: window.location.href,
                    referrer: document.referrer
                }
            });
        }
    }
});
</script>
